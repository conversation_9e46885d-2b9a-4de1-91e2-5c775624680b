#!/usr/bin/env python3
"""
测试修复后的分析脚本
"""

import os
import sys
import subprocess
import datetime

def test_fixed_analysis():
    """测试修复后的分析功能"""
    print("开始测试修复后的分析功能...")
    
    # 创建测试输出目录
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    test_output = f"test_fixed_output_{timestamp}"
    
    try:
        # 运行修复后的分析脚本
        print("运行 run_analysis.py...")
        
        cmd = [
            sys.executable, 
            "src/run_analysis.py", 
            "--data_path", "data/Si2025_5_4.csv",
            "--target_column", "Effective_Silicon"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 运行命令并捕获输出
        result = subprocess.run(
            cmd, 
            cwd="/Users/<USER>/Documents/Python/agentlaboratory624",
            capture_output=True, 
            text=True, 
            timeout=300  # 5分钟超时
        )
        
        print(f"返回码: {result.returncode}")
        
        if result.stdout:
            print("标准输出:")
            print(result.stdout[-2000:])  # 显示最后2000个字符
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr[-1000:])  # 显示最后1000个字符
        
        if result.returncode == 0:
            print("✅ 分析脚本运行成功！")
            
            # 检查输出文件
            outputs_dir = "outputs/silicon_analysis_plots"
            if os.path.exists(outputs_dir):
                print(f"\n📁 输出目录内容:")
                for root, dirs, files in os.walk(outputs_dir):
                    level = root.replace(outputs_dir, '').count(os.sep)
                    indent = ' ' * 2 * level
                    print(f"{indent}{os.path.basename(root)}/")
                    subindent = ' ' * 2 * (level + 1)
                    for file in files:
                        print(f"{subindent}{file}")
            
            return True
        else:
            print("❌ 分析脚本运行失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 脚本运行超时")
        return False
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False

if __name__ == "__main__":
    success = test_fixed_analysis()
    if success:
        print("\n🎉 所有修复都已成功应用！")
    else:
        print("\n⚠️ 仍有问题需要解决")
