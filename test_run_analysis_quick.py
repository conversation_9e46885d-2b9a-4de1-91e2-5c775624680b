#!/usr/bin/env python3
"""
快速测试 run_analysis.py 中 SHAP 可视化的修复版本
"""

import os
import datetime
import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import OneHotEncoder, RobustScaler
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
import shap

def quick_test_shap():
    """快速测试 SHAP 功能"""
    print("开始快速测试 SHAP 功能...")
    
    # 创建输出目录
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_folder = f"outputs/quick_test_{timestamp}"
    os.makedirs(output_folder, exist_ok=True)
    print(f"输出目录: {output_folder}")
    
    try:
        # 1. 加载和预处理数据
        print("1. 加载数据...")
        df = pd.read_csv("data/Si2025_5_4.csv")
        target_column = "Effective_Silicon"
        
        # 简单特征工程
        df['Na2CO3_CaOH2_Ratio'] = df['Na2CO3'] / (df['Ca(OH)2'] + 1e-8)
        df['Temp_Time_Interaction'] = df['Temp'] * df['Time']
        
        X = df.drop(columns=[target_column])
        y = df[target_column]
        
        # 识别特征类型
        numeric_features = X.select_dtypes(include=[np.number]).columns.tolist()
        categorical_features = X.select_dtypes(include=['object']).columns.tolist()
        
        print(f"数值特征: {numeric_features}")
        print(f"分类特征: {categorical_features}")
        
        # 创建预处理器
        preprocessor = ColumnTransformer(
            transformers=[
                ('num', RobustScaler(), numeric_features),
                ('cat', OneHotEncoder(handle_unknown='ignore', sparse_output=False), categorical_features)
            ]
        )
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 预处理
        X_train_processed = preprocessor.fit_transform(X_train)
        X_test_processed = preprocessor.transform(X_test)
        
        # 获取特征名称
        try:
            feature_names_list = preprocessor.get_feature_names_out()
        except AttributeError:
            # 备用方案
            num_features = list(X_train.select_dtypes(include=np.number).columns)
            cat_transformer = preprocessor.named_transformers_.get('cat')
            if cat_transformer and hasattr(cat_transformer, 'get_feature_names_out'):
                ohe_features = list(cat_transformer.get_feature_names_out())
            else:
                ohe_features = []
            feature_names_list = num_features + ohe_features
        
        X_test_processed_df = pd.DataFrame(X_test_processed, columns=feature_names_list)
        
        print(f"处理后特征数量: {len(feature_names_list)}")
        
        # 2. 训练模型
        print("2. 训练随机森林模型...")
        final_best_model_instance = RandomForestRegressor(n_estimators=50, random_state=42)  # 减少树的数量以加快速度
        final_best_model_instance.fit(X_train_processed, y_train)
        
        test_score = final_best_model_instance.score(X_test_processed, y_test)
        print(f"测试集 R²: {test_score:.4f}")
        
        final_best_model_name = "RandomForest"
        plot_paths = {}
        
        # 3. 测试修复后的 SHAP 代码
        print("3. 测试 SHAP 可视化...")
        
        try:
            # 根据模型类型选择合适的 SHAP 解释器
            if hasattr(final_best_model_instance, 'feature_importances_'):  # 树模型
                explainer = shap.TreeExplainer(final_best_model_instance)
                # 使用较小的样本以提高性能
                sample_size = min(100, len(X_test_processed_df))
                X_sample = X_test_processed_df.sample(n=sample_size, random_state=42)
                shap_values = explainer.shap_values(X_sample)
            else:  # 其他模型
                explainer = shap.Explainer(final_best_model_instance.predict, X_test_processed_df.head(50))
                sample_size = min(50, len(X_test_processed_df))
                X_sample = X_test_processed_df.sample(n=sample_size, random_state=42)
                shap_values = explainer(X_sample)
            
            print(f"SHAP 值计算完成，形状: {shap_values.shape}")
            
            # 调用 SHAP 绘图函数
            from src.analysis_utils.interpretation import _plot_shap_summary_plot
            shap_plot_paths = _plot_shap_summary_plot(shap_values, X_sample, output_folder, final_best_model_name)
            
            # 将 SHAP 图路径添加到 plot_paths 字典
            for i, path in enumerate(shap_plot_paths):
                if 'bar' in path:
                    plot_paths[f"{final_best_model_name}_shap_bar"] = path
                elif 'dot' in path:
                    plot_paths[f"{final_best_model_name}_shap_dot"] = path
            
            print("SHAP 可视化完成！")
            
        except Exception as e:
            print(f"警告: 无法为最佳模型 {final_best_model_name} 创建 SHAP/PDP 图。原因: {e}")
            import traceback
            traceback.print_exc()
        
        # 4. 输出结果
        print("\n--- 生成的图表路径 ---")
        for key, path in plot_paths.items():
            print(f"{key}: {path}")
        
        # 检查文件是否存在
        print("\n--- 文件检查 ---")
        for key, path in plot_paths.items():
            if os.path.exists(path):
                print(f"✅ {key}: 文件存在")
            else:
                print(f"❌ {key}: 文件不存在")
        
        print(f"\n✅ 快速测试完成！输出目录: {output_folder}")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    quick_test_shap()
