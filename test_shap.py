#!/usr/bin/env python3
"""
测试 SHAP 可视化功能的简化脚本
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 确保在无头环境中工作
import matplotlib.pyplot as plt
import shap
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import OneHotEncoder, StandardScaler
from sklearn.compose import ColumnTransformer
import os

def test_shap_visualization():
    """测试 SHAP 可视化功能"""
    print("开始测试 SHAP 可视化...")
    
    # 创建输出目录
    output_dir = "test_shap_output"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 1. 加载数据
        print("1. 加载数据...")
        df = pd.read_csv("data/Si2025_5_4.csv")
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        
        # 2. 简单的数据预处理
        print("2. 数据预处理...")
        target_column = "Effective_Silicon"
        
        # 分离特征和目标
        X = df.drop(columns=[target_column])
        y = df[target_column]
        
        # 识别数值和分类特征
        numeric_features = X.select_dtypes(include=[np.number]).columns.tolist()
        categorical_features = X.select_dtypes(include=['object']).columns.tolist()
        
        print(f"数值特征: {numeric_features}")
        print(f"分类特征: {categorical_features}")
        
        # 创建预处理器（处理未知类别）
        preprocessor = ColumnTransformer(
            transformers=[
                ('num', StandardScaler(), numeric_features),
                ('cat', OneHotEncoder(drop='first', sparse_output=False, handle_unknown='ignore'), categorical_features)
            ]
        )
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 预处理数据
        X_train_processed = preprocessor.fit_transform(X_train)
        X_test_processed = preprocessor.transform(X_test)
        
        # 获取特征名称
        feature_names = (numeric_features + 
                        [f"{cat}_{val}" for cat, vals in zip(categorical_features, preprocessor.named_transformers_['cat'].categories_) for val in vals[1:]])
        
        print(f"处理后的特征数量: {X_train_processed.shape[1]}")
        print(f"特征名称: {feature_names[:10]}...")  # 只显示前10个
        
        # 3. 训练模型
        print("3. 训练随机森林模型...")
        model = RandomForestRegressor(n_estimators=100, random_state=42)
        model.fit(X_train_processed, y_train)
        
        # 评估模型
        train_score = model.score(X_train_processed, y_train)
        test_score = model.score(X_test_processed, y_test)
        print(f"训练集 R²: {train_score:.4f}")
        print(f"测试集 R²: {test_score:.4f}")
        
        # 4. 创建 SHAP 解释器
        print("4. 创建 SHAP 解释器...")
        
        # 转换为 DataFrame 以便 SHAP 使用
        X_test_df = pd.DataFrame(X_test_processed, columns=feature_names)
        X_train_df = pd.DataFrame(X_train_processed, columns=feature_names)
        
        # 使用 TreeExplainer 对随机森林
        explainer = shap.TreeExplainer(model)
        
        # 计算 SHAP 值（使用较小的样本以加快速度）
        sample_size = min(50, len(X_test_df))
        X_sample = X_test_df.sample(n=sample_size, random_state=42)
        print(f"计算 {sample_size} 个样本的 SHAP 值...")
        
        shap_values = explainer.shap_values(X_sample)
        print(f"SHAP 值形状: {shap_values.shape}")
        
        # 5. 生成 SHAP 可视化
        print("5. 生成 SHAP 可视化...")
        
        # 5.1 SHAP 摘要图（条形图）
        try:
            print("  生成 SHAP 条形图...")
            plt.figure(figsize=(10, 8))
            shap.summary_plot(shap_values, X_sample, plot_type="bar", show=False)
            plt.title('SHAP Feature Importance', fontsize=16, fontweight='bold')
            plt.tight_layout()
            bar_path = os.path.join(output_dir, 'shap_summary_bar.png')
            plt.savefig(bar_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            print(f"    SHAP 条形图保存到: {bar_path}")
        except Exception as e:
            print(f"    错误: 无法生成 SHAP 条形图 - {e}")
        
        # 5.2 SHAP 摘要图（散点图）
        try:
            print("  生成 SHAP 散点图...")
            plt.figure(figsize=(10, 8))
            shap.summary_plot(shap_values, X_sample, show=False)
            plt.title('SHAP Summary Plot', fontsize=16, fontweight='bold')
            plt.tight_layout()
            dot_path = os.path.join(output_dir, 'shap_summary_dot.png')
            plt.savefig(dot_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            print(f"    SHAP 散点图保存到: {dot_path}")
        except Exception as e:
            print(f"    错误: 无法生成 SHAP 散点图 - {e}")
        
        # 5.3 SHAP 瀑布图（单个预测）
        try:
            print("  生成 SHAP 瀑布图...")
            plt.figure(figsize=(10, 8))
            shap.waterfall_plot(explainer.expected_value, shap_values[0], X_sample.iloc[0], show=False)
            plt.title('SHAP Waterfall Plot (First Sample)', fontsize=16, fontweight='bold')
            plt.tight_layout()
            waterfall_path = os.path.join(output_dir, 'shap_waterfall.png')
            plt.savefig(waterfall_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            print(f"    SHAP 瀑布图保存到: {waterfall_path}")
        except Exception as e:
            print(f"    错误: 无法生成 SHAP 瀑布图 - {e}")
        
        # 5.4 SHAP 依赖图（前3个重要特征）
        try:
            print("  生成 SHAP 依赖图...")
            feature_importance = np.abs(shap_values).mean(0)
            top_features_idx = np.argsort(feature_importance)[-3:]  # 前3个重要特征
            
            for i, feature_idx in enumerate(top_features_idx):
                feature_name = feature_names[feature_idx]
                plt.figure(figsize=(10, 6))
                shap.dependence_plot(feature_idx, shap_values, X_sample, show=False)
                plt.title(f'SHAP Dependence Plot - {feature_name}', fontsize=14, fontweight='bold')
                plt.tight_layout()
                dep_path = os.path.join(output_dir, f'shap_dependence_{i}_{feature_name.replace("/", "_")}.png')
                plt.savefig(dep_path, dpi=300, bbox_inches='tight', facecolor='white')
                plt.close()
                print(f"    SHAP 依赖图保存到: {dep_path}")
        except Exception as e:
            print(f"    错误: 无法生成 SHAP 依赖图 - {e}")
        
        print("\n✅ SHAP 可视化测试完成！")
        print(f"所有图表保存在: {output_dir}/")
        
        # 列出生成的文件
        generated_files = [f for f in os.listdir(output_dir) if f.endswith('.png')]
        print(f"生成的文件: {generated_files}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_shap_visualization()
