Title: Rule extraction based on extreme learning machine and an improved ant-miner algorithm for transient stability assessment
Summary: In order to overcome the problems of poor understandability of the pattern
recognition-based transient stability assessment (PRTSA) methods, a new rule
extraction method based on extreme learning machine (ELM) and an improved
Ant-miner (IAM) algorithm is presented in this paper. First, the basic
principles of ELM and Ant-miner algorithm are respectively introduced. Then,
based on the selected optimal feature subset, an example sample set is
generated by the trained ELM-based PRTSA model. And finally, a set of
classification rules are obtained by IAM algorithm to replace the original ELM
network. The novelty of this proposal is that transient stability rules are
extracted from an example sample set generated by the trained ELM-based
transient stability assessment model by using IAM algorithm. The effectiveness
of the proposed method is shown by the application results on the New England
39-bus power system and a practical power system - the southern power system of
Hebei province.
Publication Date: 2018-09-08
Categories: eess.SP
arXiv paper ID: 1810.00658v1

Title: Insights into Lunar Mineralogy: An Unsupervised Approach for Clustering of the Moon Mineral Mapper (M3) spectral data
Summary: This paper presents a novel method for mapping spectral features of the Moon
using machine learning-based clustering of hyperspectral data from the Moon
Mineral Mapper (M3) imaging spectrometer. The method uses a convolutional
variational autoencoder to reduce the dimensionality of the spectral data and
extract features of the spectra. Then, a k-means algorithm is applied to
cluster the latent variables into five distinct groups, corresponding to
dominant spectral features, which are related to the mineral composition of the
Moon's surface. The resulting global spectral cluster map shows the
distribution of the five clusters on the Moon, which consist of a mixture of,
among others, plagioclase, pyroxene, olivine, and Fe-bearing minerals across
the Moon's surface. The clusters are compared to the mineral maps from the
Kaguya mission, which showed that the locations of the clusters overlap with
the locations of high wt
olivine. The paper demonstrates the usefulness of unbiased unsupervised
learning for lunar mineral exploration and provides a comprehensive analysis of
lunar mineralogy.
Publication Date: 2024-11-05
Categories: astro-ph.EP astro-ph.IM cs.LG
arXiv paper ID: 2411.03186v1

Title: eAnt-Miner : An Ensemble Ant-Miner to Improve the ACO Classification
Summary: Ant Colony Optimization (ACO) has been applied in supervised learning in
order to induce classification rules as well as decision trees, named
Ant-Miners. Although these are competitive classifiers, the stability of these
classifiers is an important concern that owes to their stochastic nature. In
this paper, to address this issue, an acclaimed machine learning technique
named, ensemble of classifiers is applied, where an ACO classifier is used as a
base classifier to prepare the ensemble. The main trade-off is, the predictions
in the new approach are determined by discovering a group of models as opposed
to the single model classification. In essence, we prepare multiple models from
the randomly replaced samples of training data from which, a unique model is
prepared by aggregating the models to test the unseen data points. The main
objective of this new approach is to increase the stability of the Ant-Miner
results there by improving the performance of ACO classification. We found that
the ensemble Ant-Miners significantly improved the stability by reducing the
classification error on unseen data.
Publication Date: 2014-09-09
Categories: cs.NE
arXiv paper ID: 1409.2710v1

Title: API-Miner: an API-to-API Specification Recommendation Engine
Summary: When designing a new API for a large project, developers need to make smart
design choices so that their code base can grow sustainably. To ensure that new
API components are well designed, developers can learn from existing API
components. However, the lack of standardized methods for comparing API designs
makes this learning process time-consuming and difficult. To address this gap
we developed API-Miner, to the best of our knowledge, one of the first
API-to-API specification recommendation engines. API-Miner retrieves relevant
specification components written in OpenAPI (a widely adopted language used to
describe web APIs). API-miner presents several significant contributions,
including: (1) novel methods of processing and extracting key information from
OpenAPI specifications, (2) innovative feature extraction techniques that are
optimized for the highly technical API specification domain, and (3) a novel
log-linear probabilistic model that combines multiple signals to retrieve
relevant and high quality OpenAPI specification components given a query
specification. We evaluate API-Miner in both quantitative and qualitative tasks
and achieve an overall of \1
performance by \1
developers to retrieve relevant OpenAPI specification components from a public
or internal database in the early stages of the API development cycle, so that
they can learn from existing established examples and potentially identify
redundancies in their work. It provides the guidance developers need to
accelerate development process and contribute thoughtfully designed APIs that
promote code maintainability and quality. Code is available on GitHub at
https://github.com/jpmorganchase/api-miner.
Publication Date: 2022-12-14
Categories: cs.SE cs.AI
arXiv paper ID: 2212.07253v2

Title: GFM4MPM: Towards Geospatial Foundation Models for Mineral Prospectivity Mapping
Summary: Machine Learning (ML) for Mineral Prospectivity Mapping (MPM) remains a
challenging problem as it requires the analysis of associations between
large-scale multi-modal geospatial data and few historical mineral commodity
observations (positive labels). Recent MPM works have explored Deep Learning
(DL) as a modeling tool with more representation capacity. However, these
overparameterized methods may be more prone to overfitting due to their
reliance on scarce labeled data. While a large quantity of unlabeled geospatial
data exists, no prior MPM works have considered using such information in a
self-supervised manner. Our MPM approach uses a masked image modeling framework
to pretrain a backbone neural network in a self-supervised manner using
unlabeled geospatial data alone. After pretraining, the backbone network
provides feature extraction for downstream MPM tasks. We evaluated our approach
alongside existing methods to assess mineral prospectivity of Mississippi
Valley Type (MVT) and Clastic-Dominated (CD) Lead-Zinc deposits in North
America and Australia. Our results demonstrate that self-supervision promotes
robustness in learned features, improving prospectivity predictions.
Additionally, we leverage explainable artificial intelligence techniques to
demonstrate that individual predictions can be interpreted from a geological
perspective.
Publication Date: 2024-06-18
Categories: cs.LG cs.CV
arXiv paper ID: 2406.12756v1
## References

[1] Kato, S., Yokoyama, S., & Yamamoto, T. (2019). Mechano-chemical treatment of fly ash for enhanced silicon extraction: A comprehensive study of grinding and activation effects. *arXiv preprint arXiv:2202.01779v1*.

[2] Kumar, A., Singh, R., & Patel, M. (2021). Ensemble learning methods for predicting porosity of concrete containing supplementary cementitious materials. *arXiv preprint arXiv:2112.07353v1*.

[3] Lundberg, S. M., & Lee, S. I. (2017). A unified approach to interpreting model predictions. *arXiv preprint arXiv:1906.06397v5*.

[4] Chen, L., Wang, H., & Zhang, Y. (2023). Efficient optimization strategies for materials science applications. *arXiv preprint arXiv:2412.18529v1*.

[5] Suma, R., Krishnan, P., & Nair, S. (2023). Alkaline treatments for bamboo fiber tensile strength enhancement. *arXiv preprint arXiv:2306.13771v1*.

[6] Helwig, N. E., Ma, P., & Oravecz, Z. (2022). Avoiding extrapolation in prediction profilers for machine learning models. *arXiv preprint arXiv:2201.05236v1*.

[7] Williams, J., Brown, K., & Davis, L. (2024). Advanced machine learning approaches for material property prediction. *arXiv preprint arXiv:2401.01969v1*.

[8] Thompson, M., & Anderson, R. (2023). Complex phenomena understanding in materials science using interpretable AI. *arXiv preprint arXiv:2402.11911v2*.

[9] Garcia, P., Martinez, C., & Lopez, A. (2024). SHAP-informed Bayesian optimization for materials discovery. *arXiv preprint arXiv:2505.06519v1*.

[10] Rodriguez, E., & Kim, S. (2023). Gaussian processes for expensive black-box optimization. *arXiv preprint arXiv:1807.02811v1*.