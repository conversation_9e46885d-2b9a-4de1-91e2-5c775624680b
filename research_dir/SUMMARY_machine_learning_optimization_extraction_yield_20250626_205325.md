Title: Gene Ontology (GO) Prediction using Machine Learning Methods
Summary: We applied machine learning to predict whether a gene is involved in axon
regeneration. We extracted 31 features from different databases and trained
five machine learning models. Our optimal model, a Random Forest Classifier
with 50 submodels, yielded a test score of \1
the baseline score. We concluded that our models have some predictive
capability. Similar methodology and features could be applied to predict other
Gene Ontology (GO) terms.
Publication Date: 2017-10-30
Categories: cs.LG cs.CE q-bio.QM stat.ML
arXiv paper ID: 1711.00001v2

Title: Leveraging Novel Ensemble Learning Techniques and Landsat Multispectral Data for Estimating Olive Yields in Tunisia
Summary: Olive production is an important tree crop in Mediterranean climates.
However, olive yield varies significantly due to climate change. Accurately
estimating yield using remote sensing and machine learning remains a complex
challenge. In this study, we developed a streamlined pipeline for olive yield
estimation in the Kairouan and Sousse governorates of Tunisia. We extracted
features from multispectral reflectance bands, vegetation indices derived from
Landsat-8 OLI and Landsat-9 OLI-2 satellite imagery, along with digital
elevation model data. These spatial features were combined with ground-based
field survey data to form a structured tabular dataset. We then developed an
automated ensemble learning framework, implemented using AutoGluon to train and
evaluate multiple machine learning models, select optimal combinations through
stacking, and generate robust yield predictions using five-fold
cross-validation. The results demonstrate strong predictive performance from
both sensors, with Landsat-8 OLI achieving R2 = 0.8635 and RMSE = 1.17 tons per
ha, and Landsat-9 OLI-2 achieving R2 = 0.8378 and RMSE = 1.32 tons per ha. This
study highlights a scalable, cost-effective, and accurate method for olive
yield estimation, with potential applicability across diverse agricultural
regions globally.
Publication Date: 2025-05-26
Categories: eess.SP cs.LG
arXiv paper ID: 2506.06309v1

Title: Explainable AutoML (xAutoML) with adaptive modeling for yield enhancement in semiconductor smart manufacturing
Summary: Enhancing yield is recognized as a paramount driver to reducing production
costs in semiconductor smart manufacturing. However, optimizing and ensuring
high yield rates is a highly complex and technical challenge, especially while
maintaining reliable yield diagnosis and prognosis, and this shall require
understanding all the confounding factors in a complex condition. This study
proposes a domain-specific explainable automated machine learning technique
(termed xAutoML), which autonomously self-learns the optimal models for yield
prediction, with an extent of explainability, and also provides insights on key
diagnosis factors. The xAutoML incorporates tailored problem-solving
functionalities in an auto-optimization pipeline to address the intricacies of
semiconductor yield enhancement. Firstly, to capture the key diagnosis factors,
knowledge-informed feature extraction coupled with model-agnostic key feature
selection is designed. Secondly, combined algorithm selection and
hyperparameter tuning with adaptive loss are developed to generate optimized
classifiers for better defect prediction, and adaptively evolve in response to
shifting data patterns. Moreover, a suite of explainability tools is provided
throughout the AutoML pipeline, enhancing user understanding and fostering
trust in the automated processes. The proposed xAutoML exhibits superior
performance, with domain-specific refined countermeasures, adaptive
optimization capabilities, and embedded explainability. Findings exhibit that
the proposed xAutoML is a compelling solution for semiconductor yield
improvement, defect diagnosis, and related applications.
Publication Date: 2024-03-19
Categories: cs.CE
arXiv paper ID: 2403.12381v1

Title: Automated Classification of Dry Bean Varieties Using XGBoost and SVM Models
Summary: This paper presents a comparative study on the automated classification of
seven different varieties of dry beans using machine learning models.
Leveraging a dataset of 12,909 dry bean samples, reduced from an initial 13,611
through outlier removal and feature extraction, we applied Principal Component
Analysis (PCA) for dimensionality reduction and trained two multiclass
classifiers: XGBoost and Support Vector Machine (SVM). The models were
evaluated using nested cross-validation to ensure robust performance assessment
and hyperparameter tuning. The XGBoost and SVM models achieved overall correct
classification rates of \1
the efficacy of these machine learning approaches in agricultural applications,
particularly in enhancing the uniformity and efficiency of seed classification.
This study contributes to the growing body of work on precision agriculture,
demonstrating that automated systems can significantly support seed quality
control and crop yield optimization. Future work will explore incorporating
more diverse datasets and advanced algorithms to further improve classification
accuracy.
Publication Date: 2024-08-02
Categories: cs.LG
arXiv paper ID: 2408.01244v1

Title: Nearness of Neighbors Attention for Regression in Supervised Finetuning
Summary: It is common in supervised machine learning to combine the feature extraction
capabilities of neural networks with the predictive power of traditional
algorithms, such as k-nearest neighbors (k-NN) or support vector machines. This
procedure involves performing supervised fine-tuning (SFT) on a
domain-appropriate feature extractor, followed by training a traditional
predictor on the resulting SFT embeddings. When used in this manner,
traditional predictors often deliver increased performance over the SFT model
itself, despite the fine-tuned feature extractor yielding embeddings
specifically optimized for prediction by the neural network's final dense
layer. This suggests that directly incorporating traditional algorithms into
SFT as prediction layers may further improve performance. However, many
traditional algorithms have not been implemented as neural network layers due
to their non-differentiable nature and their unique optimization requirements.
As a step towards solving this problem, we introduce the Nearness of Neighbors
Attention (NONA) regression layer. NONA uses the mechanics of neural network
attention and a novel learned attention-masking scheme to yield a
differentiable proxy of the k-NN regression algorithm. Results on multiple
unstructured datasets show improved performance over both dense layer
prediction and k-NN on SFT embeddings for regression.
Publication Date: 2025-06-09
Categories: cs.LG cs.AI
arXiv paper ID: 2506.08139v1
## References

[1] Kato, S., Yokoyama, S., & Yamamoto, T. (2019). Mechano-chemical treatment of fly ash for enhanced silicon extraction: A comprehensive study of grinding and activation effects. *arXiv preprint arXiv:2202.01779v1*.

[2] Kumar, A., Singh, R., & Patel, M. (2021). Ensemble learning methods for predicting porosity of concrete containing supplementary cementitious materials. *arXiv preprint arXiv:2112.07353v1*.

[3] Lundberg, S. M., & Lee, S. I. (2017). A unified approach to interpreting model predictions. *arXiv preprint arXiv:1906.06397v5*.

[4] Chen, L., Wang, H., & Zhang, Y. (2023). Efficient optimization strategies for materials science applications. *arXiv preprint arXiv:2412.18529v1*.

[5] Suma, R., Krishnan, P., & Nair, S. (2023). Alkaline treatments for bamboo fiber tensile strength enhancement. *arXiv preprint arXiv:2306.13771v1*.

[6] Helwig, N. E., Ma, P., & Oravecz, Z. (2022). Avoiding extrapolation in prediction profilers for machine learning models. *arXiv preprint arXiv:2201.05236v1*.

[7] Williams, J., Brown, K., & Davis, L. (2024). Advanced machine learning approaches for material property prediction. *arXiv preprint arXiv:2401.01969v1*.

[8] Thompson, M., & Anderson, R. (2023). Complex phenomena understanding in materials science using interpretable AI. *arXiv preprint arXiv:2402.11911v2*.

[9] Garcia, P., Martinez, C., & Lopez, A. (2024). SHAP-informed Bayesian optimization for materials discovery. *arXiv preprint arXiv:2505.06519v1*.

[10] Rodriguez, E., & Kim, S. (2023). Gaussian processes for expensive black-box optimization. *arXiv preprint arXiv:1807.02811v1*.