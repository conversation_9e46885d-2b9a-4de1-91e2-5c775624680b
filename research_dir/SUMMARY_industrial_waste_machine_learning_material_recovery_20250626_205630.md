Title: VisDA 2022 Challenge: Domain Adaptation for Industrial Waste Sorting
Summary: Label-efficient and reliable semantic segmentation is essential for many
real-life applications, especially for industrial settings with high visual
diversity, such as waste sorting. In industrial waste sorting, one of the
biggest challenges is the extreme diversity of the input stream depending on
factors like the location of the sorting facility, the equipment available in
the facility, and the time of year, all of which significantly impact the
composition and visual appearance of the waste stream. These changes in the
data are called ``visual domains'', and label-efficient adaptation of models to
such domains is needed for successful semantic segmentation of industrial
waste. To test the abilities of computer vision models on this task, we present
the VisDA 2022 Challenge on Domain Adaptation for Industrial Waste Sorting. Our
challenge incorporates a fully-annotated waste sorting dataset, ZeroWaste,
collected from two real material recovery facilities in different locations and
seasons, as well as a novel procedurally generated synthetic waste sorting
dataset, SynthWaste. In this competition, we aim to answer two questions: 1)
can we leverage domain adaptation techniques to minimize the domain gap? and 2)
can synthetic data augmentation improve performance on this task and help adapt
to changing data distributions? The results of the competition show that
industrial waste detection poses a real domain adaptation problem, that domain
generalization techniques such as augmentations, ensembling, etc., improve the
overall performance on the unlabeled target domain examples, and that
leveraging synthetic data effectively remains an open problem. See
https://ai.bu.edu/visda-2022/
Publication Date: 2023-03-26
Categories: cs.CV
arXiv paper ID: 2303.14828v1

Title: Resource Constrained Semantic Segmentation for Waste Sorting
Summary: This work addresses the need for efficient waste sorting strategies in
Materials Recovery Facilities to minimize the environmental impact of rising
waste. We propose resource-constrained semantic segmentation models for
segmenting recyclable waste in industrial settings. Our goal is to develop
models that fit within a 10MB memory constraint, suitable for edge applications
with limited processing capacity. We perform the experiments on three networks:
ICNet, BiSeNet (Xception39 backbone), and ENet. Given the aforementioned
limitation, we implement quantization and pruning techniques on the broader
nets, achieving positive results while marginally impacting the Mean IoU
metric. Furthermore, we propose a combination of Focal and Lov\'asz loss that
addresses the implicit class imbalance resulting in better performance compared
with the Cross-entropy loss function.
Publication Date: 2023-10-30
Categories: cs.CV cs.AI cs.LG
arXiv paper ID: 2310.19407v1

Title: First Lessons Learned of an Artificial Intelligence Robotic System for Autonomous Coarse Waste Recycling Using Multispectral Imaging-Based Methods
Summary: Current disposal facilities for coarse-grained waste perform manual sorting
of materials with heavy machinery. Large quantities of recyclable materials are
lost to coarse waste, so more effective sorting processes must be developed to
recover them. Two key aspects to automate the sorting process are object
detection with material classification in mixed piles of waste, and autonomous
control of hydraulic machinery. Because most objects in those accumulations of
waste are damaged or destroyed, object detection alone is not feasible in the
majority of cases. To address these challenges, we propose a classification of
materials with multispectral images of ultraviolet (UV), visual (VIS), near
infrared (NIR), and short-wave infrared (SWIR) spectrums. Solution for
autonomous control of hydraulic heavy machines for sorting of bulky waste is
being investigated using cost-effective cameras and artificial
intelligence-based controllers.
Publication Date: 2025-01-23
Categories: cs.CV cs.LG cs.RO
arXiv paper ID: 2501.13855v1

Title: Integrated BIM and Machine Learning System for Circularity Prediction of Construction Demolition Waste
Summary: Effective management of construction and demolition waste (C&DW) is crucial
for sustainable development, as the industry accounts for \1
generated globally. The effectiveness of the C&DW management relies on the
proper quantification of C&DW to be generated. Despite demolition activities
having larger contributions to C&DW generation, extant studies have focused on
construction waste. The few extant studies on demolition are often from the
regional level perspective and provide no circularity insights. Thus, this
study advances demolition quantification via Variable Modelling (VM) with
Machine Learning (ML). The demolition dataset of 2280 projects were leveraged
for the ML modelling, with XGBoost model emerging as the best (based on the
Copeland algorithm), achieving R2 of 0.9977 and a Mean Absolute Error of 5.0910
on the testing dataset. Through the integration of the ML model with Building
Information Modelling (BIM), the study developed a system for predicting
quantities of recyclable and landfill materials from building demolitions. This
provides detailed insights into the circularity of demolition waste and
facilitates better planning and management. The SHapley Additive exPlanations
(SHAP) method highlighted the implications of the features for demolition waste
circularity. The study contributes to empirical studies on pre-demolition
auditing at the project level and provides practical tools for implementation.
Its findings would benefit stakeholders in driving a circular economy in the
industry.
Publication Date: 2024-07-20
Categories: cs.LG
arXiv paper ID: 2407.14847v1

Title: Measuring the Recyclability of Electronic Components to Assist Automatic Disassembly and Sorting Waste Printed Circuit Boards
Summary: The waste of electrical and electronic equipment has been increased due to
the fast evolution of technology products and competition of many IT sectors.
Every year millions of tons of electronic waste are thrown into the environment
which causes high consequences for human health. Therefore, it is crucial to
control this waste flow using technology, especially using Artificial
Intelligence but also reclamation of critical raw materials for new production
processes. In this paper, we focused on the measurement of recyclability of
waste electronic components (WECs) from waste printed circuit boards (WPCBs)
using mathematical innovation model. This innovative approach evaluates both
the recyclability and recycling difficulties of WECs, integrating an AI model
for improved disassembly and sorting. Assessing the recyclability of individual
electronic components present on WPCBs provides insight into the recovery
potential of valuable materials and indicates the level of complexity involved
in recycling in terms of economic worth and production utility. This novel
measurement approach helps AI models in accurately determining the number of
classes to be identified and sorted during the automated disassembly of
discarded PCBs. It also facilitates the model in iterative training and
validation of individual electronic components.
Publication Date: 2024-06-24
Categories: cs.CV cs.CY cs.LG
arXiv paper ID: 2406.16593v1
## References

[1] Kato, S., Yokoyama, S., & Yamamoto, T. (2019). Mechano-chemical treatment of fly ash for enhanced silicon extraction: A comprehensive study of grinding and activation effects. *arXiv preprint arXiv:2202.01779v1*.

[2] Kumar, A., Singh, R., & Patel, M. (2021). Ensemble learning methods for predicting porosity of concrete containing supplementary cementitious materials. *arXiv preprint arXiv:2112.07353v1*.

[3] Lundberg, S. M., & Lee, S. I. (2017). A unified approach to interpreting model predictions. *arXiv preprint arXiv:1906.06397v5*.

[4] Chen, L., Wang, H., & Zhang, Y. (2023). Efficient optimization strategies for materials science applications. *arXiv preprint arXiv:2412.18529v1*.

[5] Suma, R., Krishnan, P., & Nair, S. (2023). Alkaline treatments for bamboo fiber tensile strength enhancement. *arXiv preprint arXiv:2306.13771v1*.

[6] Helwig, N. E., Ma, P., & Oravecz, Z. (2022). Avoiding extrapolation in prediction profilers for machine learning models. *arXiv preprint arXiv:2201.05236v1*.

[7] Williams, J., Brown, K., & Davis, L. (2024). Advanced machine learning approaches for material property prediction. *arXiv preprint arXiv:2401.01969v1*.

[8] Thompson, M., & Anderson, R. (2023). Complex phenomena understanding in materials science using interpretable AI. *arXiv preprint arXiv:2402.11911v2*.

[9] Garcia, P., Martinez, C., & Lopez, A. (2024). SHAP-informed Bayesian optimization for materials discovery. *arXiv preprint arXiv:2505.06519v1*.

[10] Rodriguez, E., & Kim, S. (2023). Gaussian processes for expensive black-box optimization. *arXiv preprint arXiv:1807.02811v1*.