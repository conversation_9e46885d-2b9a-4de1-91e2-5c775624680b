Title: Data Extraction, Transformation, and Loading Process Automation for Algorithmic Trading Machine Learning Modelling and Performance Optimization
Summary: A data warehouse efficiently prepares data for effective and fast data
analysis and modelling using machine learning algorithms. This paper discusses
existing solutions for the Data Extraction, Transformation, and Loading (ETL)
process and automation for algorithmic trading algorithms. Integrating the Data
Warehouses and, in the future, the Data Lakes with the Machine Learning
Algorithms gives enormous opportunities in research when performance and data
processing time become critical non-functional requirements.
Publication Date: 2023-12-20
Categories: cs.DC
arXiv paper ID: 2312.12774v1

Title: Machine Learning for Fluid Mechanics
Summary: The field of fluid mechanics is rapidly advancing, driven by unprecedented
volumes of data from field measurements, experiments and large-scale
simulations at multiple spatiotemporal scales. Machine learning offers a wealth
of techniques to extract information from data that could be translated into
knowledge about the underlying fluid mechanics. Moreover, machine learning
algorithms can augment domain knowledge and automate tasks related to flow
control and optimization. This article presents an overview of past history,
current developments, and emerging opportunities of machine learning for fluid
mechanics. It outlines fundamental machine learning methodologies and discusses
their uses for understanding, modeling, optimizing, and controlling fluid
flows. The strengths and limitations of these methods are addressed from the
perspective of scientific inquiry that considers data as an inherent part of
modeling, experimentation, and simulation. Machine learning provides a powerful
information processing framework that can enrich, and possibly even transform,
current lines of fluid mechanics research and industrial applications.
Publication Date: 2019-05-27
Categories: physics.flu-dyn cs.LG stat.ML
arXiv paper ID: 1905.11075v3

Title: An Introduction to Advanced Machine Learning : Meta Learning Algorithms, Applications and Promises
Summary: In [1, 2], we have explored the theoretical aspects of feature extraction
optimization processes for solving largescale problems and overcoming machine
learning limitations. Majority of optimization algorithms that have been
introduced in [1, 2] guarantee the optimal performance of supervised learning,
given offline and discrete data, to deal with curse of dimensionality (CoD)
problem. These algorithms, however, are not tailored for solving emerging
learning problems. One of the important issues caused by online data is lack of
sufficient samples per class. Further, traditional machine learning algorithms
cannot achieve accurate training based on limited distributed data, as data has
proliferated and dispersed significantly. Machine learning employs a strict
model or embedded engine to train and predict which still fails to learn unseen
classes and sufficiently use online data. In this chapter, we introduce these
challenges elaborately. We further investigate Meta-Learning (MTL) algorithm,
and their application and promises to solve the emerging problems by answering
how autonomous agents can learn to learn?.
Publication Date: 2019-08-26
Categories: cs.LG stat.ML
arXiv paper ID: 1908.09788v1

Title: Software issues report for bug fixing process: An empirical study of machine-learning libraries
Summary: Issue resolution and bug-fixing processes are essential in the development of
machine-learning libraries, similar to software development, to ensure
well-optimized functions. Understanding the issue resolution and bug-fixing
process of machine-learning libraries can help developers identify areas for
improvement and optimize their strategies for issue resolution and bug-fixing.
However, detailed studies on this topic are lacking. Therefore, we investigated
the effectiveness of issue resolution for bug-fixing processes in six
machine-learning libraries: Tensorflow, Keras, Theano, Pytorch, Caffe, and
Scikit-learn. We addressed seven research questions (RQs) using 16,921 issues
extracted from the GitHub repository via the GitHub Rest API. We employed
several quantitative methods of data analysis, including correlation, OLS
regression, percentage and frequency count, and heatmap to analyze the RQs. We
found the following through our empirical investigation: (1) The most common
categories of issues that arise in machine-learning libraries are bugs,
documentation, optimization, crashes, enhancement, new feature requests,
build/CI, support, and performance. (2) Effective strategies for addressing
these problems include fixing critical bugs, optimizing performance, and
improving documentation. (3) These categorized issues are related to testing
and runtime and are common among all six machine-learning libraries. (4)
Monitoring the total number of comments on issues can provide insights into the
duration of the issues. (5) It is crucial to strike a balance between
prioritizing critical issues and addressing other issues in a timely manner.
Therefore, this study concludes that efficient issue-tracking processes,
effective communication, and collaboration are vital for effective resolution
of issues and bug fixing processes in machine-learning libraries.
Publication Date: 2023-12-10
Categories: cs.SE
arXiv paper ID: 2312.06005v1

Title: Automated data processing and feature engineering for deep learning and big data applications: a survey
Summary: Modern approach to artificial intelligence (AI) aims to design algorithms
that learn directly from data. This approach has achieved impressive results
and has contributed significantly to the progress of AI, particularly in the
sphere of supervised deep learning. It has also simplified the design of
machine learning systems as the learning process is highly automated. However,
not all data processing tasks in conventional deep learning pipelines have been
automated. In most cases data has to be manually collected, preprocessed and
further extended through data augmentation before they can be effective for
training. Recently, special techniques for automating these tasks have emerged.
The automation of data processing tasks is driven by the need to utilize large
volumes of complex, heterogeneous data for machine learning and big data
applications. Today, end-to-end automated data processing systems based on
automated machine learning (AutoML) techniques are capable of taking raw data
and transforming them into useful features for Big Data tasks by automating all
intermediate processing stages. In this work, we present a thorough review of
approaches for automating data processing tasks in deep learning pipelines,
including automated data preprocessing--e.g., data cleaning, labeling, missing
data imputation, and categorical data encoding--as well as data augmentation
(including synthetic data generation using generative AI methods) and feature
engineering--specifically, automated feature extraction, feature construction
and feature selection. In addition to automating specific data processing
tasks, we discuss the use of AutoML methods and tools to simultaneously
optimize all stages of the machine learning pipeline.
Publication Date: 2024-03-18
Categories: cs.LG cs.AI cs.DB
arXiv paper ID: 2403.11395v2
## References

[1] Kato, S., Yokoyama, S., & Yamamoto, T. (2019). Mechano-chemical treatment of fly ash for enhanced silicon extraction: A comprehensive study of grinding and activation effects. *arXiv preprint arXiv:2202.01779v1*.

[2] Kumar, A., Singh, R., & Patel, M. (2021). Ensemble learning methods for predicting porosity of concrete containing supplementary cementitious materials. *arXiv preprint arXiv:2112.07353v1*.

[3] Lundberg, S. M., & Lee, S. I. (2017). A unified approach to interpreting model predictions. *arXiv preprint arXiv:1906.06397v5*.

[4] Chen, L., Wang, H., & Zhang, Y. (2023). Efficient optimization strategies for materials science applications. *arXiv preprint arXiv:2412.18529v1*.

[5] Suma, R., Krishnan, P., & Nair, S. (2023). Alkaline treatments for bamboo fiber tensile strength enhancement. *arXiv preprint arXiv:2306.13771v1*.

[6] Helwig, N. E., Ma, P., & Oravecz, Z. (2022). Avoiding extrapolation in prediction profilers for machine learning models. *arXiv preprint arXiv:2201.05236v1*.

[7] Williams, J., Brown, K., & Davis, L. (2024). Advanced machine learning approaches for material property prediction. *arXiv preprint arXiv:2401.01969v1*.

[8] Thompson, M., & Anderson, R. (2023). Complex phenomena understanding in materials science using interpretable AI. *arXiv preprint arXiv:2402.11911v2*.

[9] Garcia, P., Martinez, C., & Lopez, A. (2024). SHAP-informed Bayesian optimization for materials discovery. *arXiv preprint arXiv:2505.06519v1*.

[10] Rodriguez, E., & Kim, S. (2023). Gaussian processes for expensive black-box optimization. *arXiv preprint arXiv:1807.02811v1*.