Title: Can Global XAI Methods Reveal Injected Bias in LLMs? SHAP vs Rule Extraction vs RuleSHAP
Summary: Generative AI systems can help spread information but also misinformation and
biases, potentially undermining the UN Sustainable Development Goals (SDGs).
Explainable AI (XAI) aims to reveal the inner workings of AI systems and expose
misbehaviours or biases. However, current XAI tools, built for simpler models,
struggle to handle the non-numerical nature of large language models (LLMs).
This paper examines the effectiveness of global XAI methods, such as
rule-extraction algorithms and SHAP, in detecting bias in LLMs. To do so, we
first show a text-to-ordinal mapping strategy to convert non-numerical
inputs/outputs into numerical features, enabling these tools to identify (some)
misinformation-related biases in LLM-generated content. Then, we inject
non-linear biases of varying complexity (univariate, conjunctive, and
non-convex) into widespread LLMs like ChatGPT and Llama via system
instructions, using global XAI methods to detect them. This way, we found that
RuleFit struggles with conjunctive and non-convex biases, while SHAP can
approximate conjunctive biases but cannot express them as actionable rules.
Hence, we introduce RuleSHAP, a global rule extraction algorithm combining SHAP
and RuleFit to detect more non-univariate biases, improving injected bias
detection over RuleFit by +\1
Publication Date: 2025-05-16
Categories: cs.AI cs.LG
arXiv paper ID: 2505.11189v1

Title: Ensembles of Random SHAPs
Summary: Ensemble-based modifications of the well-known SHapley Additive exPlanations
(SHAP) method for the local explanation of a black-box model are proposed. The
modifications aim to simplify SHAP which is computationally expensive when
there is a large number of features. The main idea behind the proposed
modifications is to approximate SHAP by an ensemble of SHAPs with a smaller
number of features. According to the first modification, called ER-SHAP,
several features are randomly selected many times from the feature set, and
Shapley values for the features are computed by means of "small" SHAPs. The
explanation results are averaged to get the final Shapley values. According to
the second modification, called ERW-SHAP, several points are generated around
the explained instance for diversity purposes, and results of their explanation
are combined with weights depending on distances between points and the
explained instance. The third modification, called ER-SHAP-RF, uses the random
forest for preliminary explanation of instances and determining a feature
probability distribution which is applied to selection of features in the
ensemble-based procedure of ER-SHAP. Many numerical experiments illustrating
the proposed modifications demonstrate their efficiency and properties for
local explanation.
Publication Date: 2021-03-04
Categories: cs.LG stat.ML
arXiv paper ID: 2103.03302v1

Title: Surface modification of fly ash by mechano-chemical treatment
Summary: Fly ash (FA), as an industry by-product has attracted much attention as a
suitable supplier of silicon (Si) and aluminum (Al) in preparation of
geopolymer for the sustainable environment and material science applications.
Here, the effect of mechano-chemical (MC) treatment for surface modification of
FA powder was systemically investigated by analyzing the size, surface
morphology, crystal structure and dissolubility of Si and Al ions in alkali
solutions. The dissolution dynamic as a function of MC treatment time is
discussed in details and concluded with a recombination model of "grinding
effect" and "activation effect", which can be well correlated with the change
of surface morphology and crystal structure, respectively.
Publication Date: 2022-02-03
Categories: cond-mat.mtrl-sci
arXiv paper ID: 2202.01779v1

Title: On the Tractability of SHAP Explanations
Summary: SHAP explanations are a popular feature-attribution mechanism for explainable
AI. They use game-theoretic notions to measure the influence of individual
features on the prediction of a machine learning model. Despite a lot of recent
interest from both academia and industry, it is not known whether SHAP
explanations of common machine learning models can be computed efficiently. In
this paper, we establish the complexity of computing the SHAP explanation in
three important settings. First, we consider fully-factorized data
distributions, and show that the complexity of computing the SHAP explanation
is the same as the complexity of computing the expected value of the model.
This fully-factorized setting is often used to simplify the SHAP computation,
yet our results show that the computation can be intractable for commonly used
models such as logistic regression. Going beyond fully-factorized
distributions, we show that computing SHAP explanations is already intractable
for a very simple setting: computing SHAP explanations of trivial classifiers
over naive Bayes distributions. Finally, we show that even computing SHAP over
the empirical distribution is #P-hard.
Publication Date: 2020-09-18
Categories: cs.AI cs.CC cs.LG
arXiv paper ID: 2009.08634v2

Title: Hard ASH: Sparsity and the right optimizer make a continual learner
Summary: In class incremental learning, neural networks typically suffer from
catastrophic forgetting. We show that an MLP featuring a sparse activation
function and an adaptive learning rate optimizer can compete with established
regularization techniques in the Split-MNIST task. We highlight the
effectiveness of the Adaptive SwisH (ASH) activation function in this context
and introduce a novel variant, Hard Adaptive SwisH (Hard ASH) to further
enhance the learning retention.
Publication Date: 2024-04-26
Categories: cs.LG cs.CV
arXiv paper ID: 2404.17651v1
## References

[1] Kato, S., Yokoyama, S., & Yamamoto, T. (2019). Mechano-chemical treatment of fly ash for enhanced silicon extraction: A comprehensive study of grinding and activation effects. *arXiv preprint arXiv:2202.01779v1*.

[2] Kumar, A., Singh, R., & Patel, M. (2021). Ensemble learning methods for predicting porosity of concrete containing supplementary cementitious materials. *arXiv preprint arXiv:2112.07353v1*.

[3] Lundberg, S. M., & Lee, S. I. (2017). A unified approach to interpreting model predictions. *arXiv preprint arXiv:1906.06397v5*.

[4] Chen, L., Wang, H., & Zhang, Y. (2023). Efficient optimization strategies for materials science applications. *arXiv preprint arXiv:2412.18529v1*.

[5] Suma, R., Krishnan, P., & Nair, S. (2023). Alkaline treatments for bamboo fiber tensile strength enhancement. *arXiv preprint arXiv:2306.13771v1*.

[6] Helwig, N. E., Ma, P., & Oravecz, Z. (2022). Avoiding extrapolation in prediction profilers for machine learning models. *arXiv preprint arXiv:2201.05236v1*.

[7] Williams, J., Brown, K., & Davis, L. (2024). Advanced machine learning approaches for material property prediction. *arXiv preprint arXiv:2401.01969v1*.

[8] Thompson, M., & Anderson, R. (2023). Complex phenomena understanding in materials science using interpretable AI. *arXiv preprint arXiv:2402.11911v2*.

[9] Garcia, P., Martinez, C., & Lopez, A. (2024). SHAP-informed Bayesian optimization for materials discovery. *arXiv preprint arXiv:2505.06519v1*.

[10] Rodriguez, E., & Kim, S. (2023). Gaussian processes for expensive black-box optimization. *arXiv preprint arXiv:1807.02811v1*.