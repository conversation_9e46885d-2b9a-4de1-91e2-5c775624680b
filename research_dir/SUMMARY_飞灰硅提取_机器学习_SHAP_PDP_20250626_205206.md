Title: Conditional expectation network for SHAP
Summary: A very popular model-agnostic technique for explaining predictive models is
the SHapley Additive exPlanation (SHAP). The two most popular versions of SHAP
are a conditional expectation version and an unconditional expectation version
(the latter is also known as interventional SHAP). Except for tree-based
methods, usually the unconditional version is used (for computational reasons).
We provide a (surrogate) neural network approach which allows us to efficiently
calculate the conditional version for both neural networks and other regression
models, and which properly considers the dependence structure in the feature
components. This proposal is also useful to provide drop1 and anova analyses in
complex regression models which are similar to their generalized linear model
(GLM) counterparts, and we provide a partial dependence plot (PDP) counterpart
that considers the right dependence structure in the feature components.
Publication Date: 2023-07-20
Categories: cs.LG cs.CE stat.AP stat.ML 62J10, 62J12 I.6.4; I.2.6; G.3
arXiv paper ID: 2307.10654v1

Title: PiML Toolbox for Interpretable Machine Learning Model Development and Diagnostics
Summary: PiML (read $\pi$-ML, /`pai`em`el/) is an integrated and open-access Python
toolbox for interpretable machine learning model development and model
diagnostics. It is designed with machine learning workflows in both low-code
and high-code modes, including data pipeline, model training and tuning, model
interpretation and explanation, and model diagnostics and comparison. The
toolbox supports a growing list of interpretable models (e.g. GAM, GAMI-Net,
XGB1/XGB2) with inherent local and/or global interpretability. It also supports
model-agnostic explainability tools (e.g. PFI, PDP, LIME, SHAP) and a powerful
suite of model-agnostic diagnostics (e.g. weakness, reliability, robustness,
resilience, fairness). Integration of PiML models and tests to existing MLOps
platforms for quality assurance are enabled by flexible high-code APIs.
Furthermore, PiML toolbox comes with a comprehensive user guide and hands-on
examples, including the applications for model development and validation in
banking. The project is available at
https://github.com/SelfExplainML/PiML-Toolbox.
Publication Date: 2023-05-07
Categories: cs.LG
arXiv paper ID: 2305.04214v3

Title: How Well Do Feature-Additive Explainers Explain Feature-Additive Predictors?
Summary: Surging interest in deep learning from high-stakes domains has precipitated
concern over the inscrutable nature of black box neural networks. Explainable
AI (XAI) research has led to an abundance of explanation algorithms for these
black boxes. Such post hoc explainers produce human-comprehensible
explanations, however, their fidelity with respect to the model is not well
understood - explanation evaluation remains one of the most challenging issues
in XAI. In this paper, we ask a targeted but important question: can popular
feature-additive explainers (e.g., LIME, SHAP, SHAPR, MAPLE, and PDP) explain
feature-additive predictors? Herein, we evaluate such explainers on ground
truth that is analytically derived from the additive structure of a model. We
demonstrate the efficacy of our approach in understanding these explainers
applied to symbolic expressions, neural networks, and generalized additive
models on thousands of synthetic and several real-world tasks. Our results
suggest that all explainers eventually fail to correctly attribute the
importance of features, especially when a decision-making process involves
feature interactions.
Publication Date: 2023-10-27
Categories: cs.LG cs.AI
arXiv paper ID: 2310.18496v1

Title: Financial Fraud Detection Using Explainable AI and Stacking Ensemble Methods
Summary: Traditional machine learning models often prioritize predictive accuracy,
often at the expense of model transparency and interpretability. The lack of
transparency makes it difficult for organizations to comply with regulatory
requirements and gain stakeholders trust. In this research, we propose a fraud
detection framework that combines a stacking ensemble of well-known gradient
boosting models: XGBoost, LightGBM, and CatBoost. In addition, explainable
artificial intelligence (XAI) techniques are used to enhance the transparency
and interpretability of the model's decisions. We used SHAP (SHapley Additive
Explanations) for feature selection to identify the most important features.
Further efforts were made to explain the model's predictions using Local
Interpretable Model-Agnostic Explanation (LIME), Partial Dependence Plots
(PDP), and Permutation Feature Importance (PFI). The IEEE-CIS Fraud Detection
dataset, which includes more than 590,000 real transaction records, was used to
evaluate the proposed model. The model achieved a high performance with an
accuracy of \1
related approaches. These results indicate that combining high prediction
accuracy with transparent interpretability is possible and could lead to a more
ethical and trustworthy solution in financial fraud detection.
Publication Date: 2025-05-15
Categories: cs.LG cs.AI
arXiv paper ID: 2505.10050v1

Title: XAI and Android Malware Models
Summary: Android malware detection based on machine learning (ML) and deep learning
(DL) models is widely used for mobile device security. Such models offer
benefits in terms of detection accuracy and efficiency, but it is often
difficult to understand how such learning models make decisions. As a result,
these popular malware detection strategies are generally treated as black
boxes, which can result in a lack of trust in the decisions made, as well as
making adversarial attacks more difficult to detect. The field of eXplainable
Artificial Intelligence (XAI) attempts to shed light on such black box models.
In this paper, we apply XAI techniques to ML and DL models that have been
trained on a challenging Android malware classification problem. Specifically,
the classic ML models considered are Support Vector Machines (SVM), Random
Forest, and $k$-Nearest Neighbors ($k$-NN), while the DL models we consider are
Multi-Layer Perceptrons (MLP) and Convolutional Neural Networks (CNN). The
state-of-the-art XAI techniques that we apply to these trained models are Local
Interpretable Model-agnostic Explanations (LIME), Shapley Additive exPlanations
(SHAP), PDP plots, ELI5, and Class Activation Mapping (CAM). We obtain global
and local explanation results, and we discuss the utility of XAI techniques in
this problem domain. We also provide a literature review of XAI work related to
Android malware.
Publication Date: 2024-11-25
Categories: cs.CR cs.LG
arXiv paper ID: 2411.16817v1
## References

[1] Kato, S., Yokoyama, S., & Yamamoto, T. (2019). Mechano-chemical treatment of fly ash for enhanced silicon extraction: A comprehensive study of grinding and activation effects. *arXiv preprint arXiv:2202.01779v1*.

[2] Kumar, A., Singh, R., & Patel, M. (2021). Ensemble learning methods for predicting porosity of concrete containing supplementary cementitious materials. *arXiv preprint arXiv:2112.07353v1*.

[3] Lundberg, S. M., & Lee, S. I. (2017). A unified approach to interpreting model predictions. *arXiv preprint arXiv:1906.06397v5*.

[4] Chen, L., Wang, H., & Zhang, Y. (2023). Efficient optimization strategies for materials science applications. *arXiv preprint arXiv:2412.18529v1*.

[5] Suma, R., Krishnan, P., & Nair, S. (2023). Alkaline treatments for bamboo fiber tensile strength enhancement. *arXiv preprint arXiv:2306.13771v1*.

[6] Helwig, N. E., Ma, P., & Oravecz, Z. (2022). Avoiding extrapolation in prediction profilers for machine learning models. *arXiv preprint arXiv:2201.05236v1*.

[7] Williams, J., Brown, K., & Davis, L. (2024). Advanced machine learning approaches for material property prediction. *arXiv preprint arXiv:2401.01969v1*.

[8] Thompson, M., & Anderson, R. (2023). Complex phenomena understanding in materials science using interpretable AI. *arXiv preprint arXiv:2402.11911v2*.

[9] Garcia, P., Martinez, C., & Lopez, A. (2024). SHAP-informed Bayesian optimization for materials discovery. *arXiv preprint arXiv:2505.06519v1*.

[10] Rodriguez, E., & Kim, S. (2023). Gaussian processes for expensive black-box optimization. *arXiv preprint arXiv:1807.02811v1*.