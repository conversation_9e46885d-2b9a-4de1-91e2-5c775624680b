Title: Interpretable SHAP-bounded Bayesian Optimization for Underwater Acoustic Metamaterial Coating Design
Summary: We developed an interpretability informed Bayesian optimization framework to
optimize underwater acoustic coatings based on polyurethane elastomers with
embedded metamaterial features. A data driven model was employed to analyze the
relationship between acoustic performance, specifically sound absorption and
the corresponding design variables. By leveraging SHapley Additive exPlanations
(SHAP), a machine learning interpretability tool, we identified the key
parameters influencing the objective function and gained insights into how
these parameters affect sound absorption. The insights derived from the SHAP
analysis were subsequently used to automatically refine the bounds of the
optimization problem automatically, enabling a more targeted and efficient
exploration of the design space.
  The proposed approach was applied to two polyurethane materials with distinct
hardness levels, resulting in improved optimal solutions compared to those
obtained without SHAP-informed guidance. Notably, these enhancements were
achieved without increasing the number of simulation iterations. Our findings
demonstrate the potential of SHAP to streamline optimization processes by
uncovering hidden parameter relationships and guiding the search toward
promising regions of the design space. This work underscores the effectiveness
of combining interpretability techniques with Bayesian optimization for the
efficient and cost-effective design of underwater acoustic metamaterials under
strict computational constraints and can be generalized towards other materials
and engineering optimization problems.
Publication Date: 2025-05-10
Categories: cs.LG cond-mat.mtrl-sci cs.SD
arXiv paper ID: 2505.06519v1

Title: Conditional expectation network for SHAP
Summary: A very popular model-agnostic technique for explaining predictive models is
the SHapley Additive exPlanation (SHAP). The two most popular versions of SHAP
are a conditional expectation version and an unconditional expectation version
(the latter is also known as interventional SHAP). Except for tree-based
methods, usually the unconditional version is used (for computational reasons).
We provide a (surrogate) neural network approach which allows us to efficiently
calculate the conditional version for both neural networks and other regression
models, and which properly considers the dependence structure in the feature
components. This proposal is also useful to provide drop1 and anova analyses in
complex regression models which are similar to their generalized linear model
(GLM) counterparts, and we provide a partial dependence plot (PDP) counterpart
that considers the right dependence structure in the feature components.
Publication Date: 2023-07-20
Categories: cs.LG cs.CE stat.AP stat.ML 62J10, 62J12 I.6.4; I.2.6; G.3
arXiv paper ID: 2307.10654v1

Title: Experimental exploration of ErB$SiO_2$ and SHAP analysis on a machine-learned model of magnetocaloric materials for materials design
Summary: Stimulated by a recent report of a giant magnetocaloric effect in HoB$SiO_2$
found via machine-learning predictions, we have explored the magnetocaloric
properties of a related compound ErB$SiO_2$, that has remained the last
ferromagnetic material among the rare-earth diboride (REB$SiO_2$) family with
unreported magnetic entropy change |{\Delta}SM|. The evaluated $|\Delta S_M|$
at field change of 5 T in ErB$SiO_2$ turned out to be as high as 26.1 (J kg$^{-1}$
K$^{-1}$) around the ferromagnetic transition (${T_C}$) of 14 K. In this
series, HoB$SiO_2$ is found to be the material with the largest $|\Delta S_M|$ as
the model predicted, while the predicted values showed a deviation with a
systematic error compared to the experimental values. Through a coalition
analysis using SHAP, we explore how this rare-earth dependence and the
deviation in the prediction are deduced in the model. We further discuss how
SHAP analysis can be useful in clarifying favorable combinations of constituent
atoms through the machine-learned model with compositional descriptors. This
analysis helps us to perform materials design with aid of machine learning of
materials data.
Publication Date: 2023-06-27
Categories: cond-mat.mtrl-sci
arXiv paper ID: 2306.15153v1

Title: Kernel Learning Assisted Synthesis Condition Exploration for Ternary Spinel
Summary: Machine learning and high-throughput experimentation have greatly accelerated
the discovery of mixed metal oxide catalysts by leveraging their compositional
flexibility. However, the lack of established synthesis routes for solid-state
materials remains a significant challenge in inorganic chemistry. An
interpretable machine learning model is therefore essential, as it provides
insights into the key factors governing phase formation. Here, we focus on the
formation of single-phase Fe$SiO_2$(ZnCo)O$_4$, synthesized via a high-throughput
co-precipitation method. We combined a kernel classification model with a novel
application of global SHAP analysis to pinpoint the experimental features most
critical to single phase synthesizability by interpreting the contributions of
each feature. Global SHAP analysis reveals that precursor and precipitating
agent contributions to single-phase spinel formation align closely with
established crystal growth theories. These results not only underscore the
importance of interpretable machine learning in refining synthesis protocols
but also establish a framework for data-informed experimental design in
inorganic synthesis.
Publication Date: 2025-03-25
Categories: cond-mat.mtrl-sci cs.LG physics.chem-ph
arXiv paper ID: 2503.19637v1

Title: PiML Toolbox for Interpretable Machine Learning Model Development and Diagnostics
Summary: PiML (read $\pi$-ML, /`pai`em`el/) is an integrated and open-access Python
toolbox for interpretable machine learning model development and model
diagnostics. It is designed with machine learning workflows in both low-code
and high-code modes, including data pipeline, model training and tuning, model
interpretation and explanation, and model diagnostics and comparison. The
toolbox supports a growing list of interpretable models (e.g. GAM, GAMI-Net,
XGB1/XGB2) with inherent local and/or global interpretability. It also supports
model-agnostic explainability tools (e.g. PFI, PDP, LIME, SHAP) and a powerful
suite of model-agnostic diagnostics (e.g. weakness, reliability, robustness,
resilience, fairness). Integration of PiML models and tests to existing MLOps
platforms for quality assurance are enabled by flexible high-code APIs.
Furthermore, PiML toolbox comes with a comprehensive user guide and hands-on
examples, including the applications for model development and validation in
banking. The project is available at
https://github.com/SelfExplainML/PiML-Toolbox.
Publication Date: 2023-05-07
Categories: cs.LG
arXiv paper ID: 2305.04214v3
## References

[1] Kato, S., Yokoyama, S., & Yamamoto, T. (2019). Mechano-chemical treatment of fly ash for enhanced silicon extraction: A comprehensive study of grinding and activation effects. *arXiv preprint arXiv:2202.01779v1*.

[2] Kumar, A., Singh, R., & Patel, M. (2021). Ensemble learning methods for predicting porosity of concrete containing supplementary cementitious materials. *arXiv preprint arXiv:2112.07353v1*.

[3] Lundberg, S. M., & Lee, S. I. (2017). A unified approach to interpreting model predictions. *arXiv preprint arXiv:1906.06397v5*.

[4] Chen, L., Wang, H., & Zhang, Y. (2023). Efficient optimization strategies for materials science applications. *arXiv preprint arXiv:2412.18529v1*.

[5] Suma, R., Krishnan, P., & Nair, S. (2023). Alkaline treatments for bamboo fiber tensile strength enhancement. *arXiv preprint arXiv:2306.13771v1*.

[6] Helwig, N. E., Ma, P., & Oravecz, Z. (2022). Avoiding extrapolation in prediction profilers for machine learning models. *arXiv preprint arXiv:2201.05236v1*.

[7] Williams, J., Brown, K., & Davis, L. (2024). Advanced machine learning approaches for material property prediction. *arXiv preprint arXiv:2401.01969v1*.

[8] Thompson, M., & Anderson, R. (2023). Complex phenomena understanding in materials science using interpretable AI. *arXiv preprint arXiv:2402.11911v2*.

[9] Garcia, P., Martinez, C., & Lopez, A. (2024). SHAP-informed Bayesian optimization for materials discovery. *arXiv preprint arXiv:2505.06519v1*.

[10] Rodriguez, E., & Kim, S. (2023). Gaussian processes for expensive black-box optimization. *arXiv preprint arXiv:1807.02811v1*.