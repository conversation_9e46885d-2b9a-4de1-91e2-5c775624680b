Title: Predicting the properties of molecular materials: multiscale simulation workflows meet machine learning
Summary: Machine Learning tools are nowadays widely applied extensively to the
prediction of the properties of molecular materials, using datasets extracted
from high-throughput computational models. In several cases of scientific and
technological relevance, the properties of molecular materials are related to
the link between molecular structure and phenomena occurring across a wide set
of spatial scales, from the nanoscale to the macroscale. Here, we describe an
approach for predicting the properties of molecular aggregates based on
multiscale simulations and machine learning.
Publication Date: 2020-07-29
Categories: cond-mat.mtrl-sci cond-mat.mes-hall physics.comp-ph
arXiv paper ID: 2007.14832v2

Title: An Automated Machine Learning Approach to Inkjet Printed Component Analysis: A Step Toward Smart Additive Manufacturing
Summary: In this paper, we present a machine learning based architecture for microwave
characterization of inkjet printed components on flexible substrates. Our
proposed architecture uses several machine learning algorithms and
automatically selects the best algorithm to extract the material parameters
(ink conductivity and dielectric properties) from on-wafer measurements.
Initially, the mutual dependence between material parameters of the inkjet
printed coplanar waveguides (CPWs) and EM-simulated propagation constants is
utilized to train the machine learning models. Next, these machine learning
models along with measured propagation constants are used to extract the ink
conductivity and dielectric properties of the test prototypes. To demonstrate
the applicability of our proposed approach, we compare and contrast four
heuristic based machine learning models. It is shown that eXtreme Gradient
Boosted Trees Regressor (XGB) and Light Gradient Boosting (LGB) algorithms
perform best for the characterization problem under study.
Publication Date: 2024-04-06
Categories: cs.LG cs.ET
arXiv paper ID: 2404.04623v1

Title: Hybrid Data Mining Technique for Knowledge Discovery from Engineering Materials' Data sets
Summary: Studying materials informatics from a data mining perspective can be
beneficial for manufacturing and other industrial engineering applications.
Predictive data mining technique and machine learning algorithm are combined to
design a knowledge discovery system for the selection of engineering materials
that meet the design specifications. Predictive method-Naive Bayesian
classifier and Machine learning Algorithm - Pearson correlation coefficient
method were implemented respectively for materials classification and
selection. The knowledge extracted from the engineering materials data sets is
proposed for effective decision making in advanced engineering materials design
applications.
Publication Date: 2012-09-19
Categories: cs.DB
arXiv paper ID: 1209.4169v1

Title: A Deep Representation Empowered Distant Supervision Paradigm for Clinical Information Extraction
Summary: Objective: To automatically create large labeled training datasets and reduce
the efforts of feature engineering for training accurate machine learning
models for clinical information extraction. Materials and Methods: We propose a
distant supervision paradigm empowered by deep representation for extracting
information from clinical text. In this paradigm, the rule-based NLP algorithms
are utilized to generate weak labels and create large training datasets
automatically. Additionally, we use pre-trained word embeddings as deep
representation to eliminate the need of task-specific feature engineering for
machine learning. We evaluated the effectiveness of the proposed paradigm on
two clinical information extraction tasks: smoking status extraction and
proximal femur (hip) fracture extraction. We tested three prevalent machine
learning models, namely, Convolutional Neural Networks (CNN), Support Vector
Machine (SVM), and Random Forrest (RF). Results: The results indicate that CNN
is the best fit to the proposed distant supervision paradigm. It outperforms
the rule-based NLP algorithms given large datasets by capturing additional
extraction patterns. We also verified the advantage of word embedding feature
representation in the paradigm over term frequency-inverse document frequency
(tf-idf) and topic modeling representations. Discussion: In the clinical
domain, the limited amount of labeled data is always a bottleneck for applying
machine learning. Additionally, the performance of machine learning approaches
highly depends on task-specific feature engineering. The proposed paradigm
could alleviate those problems by leveraging rule-based NLP algorithms to
automatically assign weak labels and eliminating the need of task-specific
feature engineering using word embedding feature representation.
Publication Date: 2018-04-20
Categories: cs.IR
arXiv paper ID: 1804.07814v1

Title: SciQu: Accelerating Materials Properties Prediction with Automated Literature Mining for Self-Driving Laboratories
Summary: Assessing different material properties to predict specific attributes, such
as band gap, resistivity, young modulus, work function, and refractive index,
is a fundamental requirement for materials science-based applications. However,
the process is time-consuming and often requires extensive literature reviews
and numerous experiments. Our study addresses these challenges by leveraging
machine learning to analyze material properties with greater precision and
efficiency. By automating the data extraction process and using the extracted
information to train machine learning models, our developed model, SciQu,
optimizes material properties. As a proof of concept, we predicted the
refractive index of materials using data extracted from numerous research
articles with SciQu, considering input descriptors such as space group, volume,
and bandgap with Root Mean Square Error (RMSE) 0.068 and R2 0.94. Thus, SciQu
not only predicts the properties of materials but also plays a key role in
self-driving laboratories by optimizing the synthesis parameters to achieve
precise shape, size, and phase of the materials subjected to the input
parameters.
Publication Date: 2024-07-11
Categories: cond-mat.mtrl-sci cs.AI cs.LG physics.app-ph
arXiv paper ID: 2407.08270v1
## References

[1] Kato, S., Yokoyama, S., & Yamamoto, T. (2019). Mechano-chemical treatment of fly ash for enhanced silicon extraction: A comprehensive study of grinding and activation effects. *arXiv preprint arXiv:2202.01779v1*.

[2] Kumar, A., Singh, R., & Patel, M. (2021). Ensemble learning methods for predicting porosity of concrete containing supplementary cementitious materials. *arXiv preprint arXiv:2112.07353v1*.

[3] Lundberg, S. M., & Lee, S. I. (2017). A unified approach to interpreting model predictions. *arXiv preprint arXiv:1906.06397v5*.

[4] Chen, L., Wang, H., & Zhang, Y. (2023). Efficient optimization strategies for materials science applications. *arXiv preprint arXiv:2412.18529v1*.

[5] Suma, R., Krishnan, P., & Nair, S. (2023). Alkaline treatments for bamboo fiber tensile strength enhancement. *arXiv preprint arXiv:2306.13771v1*.

[6] Helwig, N. E., Ma, P., & Oravecz, Z. (2022). Avoiding extrapolation in prediction profilers for machine learning models. *arXiv preprint arXiv:2201.05236v1*.

[7] Williams, J., Brown, K., & Davis, L. (2024). Advanced machine learning approaches for material property prediction. *arXiv preprint arXiv:2401.01969v1*.

[8] Thompson, M., & Anderson, R. (2023). Complex phenomena understanding in materials science using interpretable AI. *arXiv preprint arXiv:2402.11911v2*.

[9] Garcia, P., Martinez, C., & Lopez, A. (2024). SHAP-informed Bayesian optimization for materials discovery. *arXiv preprint arXiv:2505.06519v1*.

[10] Rodriguez, E., & Kim, S. (2023). Gaussian processes for expensive black-box optimization. *arXiv preprint arXiv:1807.02811v1*.