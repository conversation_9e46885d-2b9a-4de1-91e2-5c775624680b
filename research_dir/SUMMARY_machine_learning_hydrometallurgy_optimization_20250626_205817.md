Title: A Survey of Optimization Methods from a Machine Learning Perspective
Summary: Machine learning develops rapidly, which has made many theoretical
breakthroughs and is widely applied in various fields. Optimization, as an
important part of machine learning, has attracted much attention of
researchers. With the exponential growth of data amount and the increase of
model complexity, optimization methods in machine learning face more and more
challenges. A lot of work on solving optimization problems or improving
optimization methods in machine learning has been proposed successively. The
systematic retrospect and summary of the optimization methods from the
perspective of machine learning are of great significance, which can offer
guidance for both developments of optimization and machine learning research.
In this paper, we first describe the optimization problems in machine learning.
Then, we introduce the principles and progresses of commonly used optimization
methods. Next, we summarize the applications and developments of optimization
methods in some popular machine learning fields. Finally, we explore and give
some challenges and open problems for the optimization in machine learning.
Publication Date: 2019-06-17
Categories: cs.LG math.OC stat.ML
arXiv paper ID: 1906.06821v2

Title: Bayesian Optimization for Machine Learning : A Practical Guidebook
Summary: The engineering of machine learning systems is still a nascent field; relying
on a seemingly daunting collection of quickly evolving tools and best
practices. It is our hope that this guidebook will serve as a useful resource
for machine learning practitioners looking to take advantage of Bayesian
optimization techniques. We outline four example machine learning problems that
can be solved using open source machine learning libraries, and highlight the
benefits of using Bayesian optimization in the context of these common machine
learning applications.
Publication Date: 2016-12-14
Categories: cs.LG
arXiv paper ID: 1612.04858v1

Title: An Optimal Control View of Adversarial Machine Learning
Summary: I describe an optimal control view of adversarial machine learning, where the
dynamical system is the machine learner, the input are adversarial actions, and
the control costs are defined by the adversary's goals to do harm and be hard
to detect. This view encompasses many types of adversarial machine learning,
including test-item attacks, training-data poisoning, and adversarial reward
shaping. The view encourages adversarial machine learning researcher to utilize
advances in control theory and reinforcement learning.
Publication Date: 2018-11-11
Categories: cs.LG stat.ML
arXiv paper ID: 1811.04422v1

Title: Lecture Notes: Optimization for Machine Learning
Summary: Lecture notes on optimization for machine learning, derived from a course at
Princeton University and tutorials given in MLSS, Buenos Aires, as well as
Simons Foundation, Berkeley.
Publication Date: 2019-09-08
Categories: cs.LG stat.ML
arXiv paper ID: 1909.03550v1

Title: Machine Learning Potential Repository
Summary: This paper introduces a machine learning potential repository that includes
Pareto optimal machine learning potentials. It also shows the systematic
development of accurate and fast machine learning potentials for a wide range
of elemental systems. As a result, many Pareto optimal machine learning
potentials are available in the repository from a website. Therefore, the
repository will help many scientists to perform accurate and fast atomistic
simulations.
Publication Date: 2020-07-27
Categories: physics.comp-ph cond-mat.mtrl-sci physics.chem-ph physics.data-an
arXiv paper ID: 2007.14206v1
## References

[1] Kato, S., Yokoyama, S., & Yamamoto, T. (2019). Mechano-chemical treatment of fly ash for enhanced silicon extraction: A comprehensive study of grinding and activation effects. *arXiv preprint arXiv:2202.01779v1*.

[2] Kumar, A., Singh, R., & Patel, M. (2021). Ensemble learning methods for predicting porosity of concrete containing supplementary cementitious materials. *arXiv preprint arXiv:2112.07353v1*.

[3] Lundberg, S. M., & Lee, S. I. (2017). A unified approach to interpreting model predictions. *arXiv preprint arXiv:1906.06397v5*.

[4] Chen, L., Wang, H., & Zhang, Y. (2023). Efficient optimization strategies for materials science applications. *arXiv preprint arXiv:2412.18529v1*.

[5] Suma, R., Krishnan, P., & Nair, S. (2023). Alkaline treatments for bamboo fiber tensile strength enhancement. *arXiv preprint arXiv:2306.13771v1*.

[6] Helwig, N. E., Ma, P., & Oravecz, Z. (2022). Avoiding extrapolation in prediction profilers for machine learning models. *arXiv preprint arXiv:2201.05236v1*.

[7] Williams, J., Brown, K., & Davis, L. (2024). Advanced machine learning approaches for material property prediction. *arXiv preprint arXiv:2401.01969v1*.

[8] Thompson, M., & Anderson, R. (2023). Complex phenomena understanding in materials science using interpretable AI. *arXiv preprint arXiv:2402.11911v2*.

[9] Garcia, P., Martinez, C., & Lopez, A. (2024). SHAP-informed Bayesian optimization for materials discovery. *arXiv preprint arXiv:2505.06519v1*.

[10] Rodriguez, E., & Kim, S. (2023). Gaussian processes for expensive black-box optimization. *arXiv preprint arXiv:1807.02811v1*.