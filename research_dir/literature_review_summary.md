Provided here is a literature review on this topic:
arXiv ID: 2505.06519v1, Summary: The paper "Interpretable SHAP-bounded Bayesian Optimization for Underwater Acoustic Metamaterial Coating Design" proposes a novel framework that integrates SHapley Additive exPlanations (SHAP) with Bayesian Optimization (BO) to accelerate and enhance the design of complex materials. Specifically, Deep Neural Networks (DNNs) are employed as surrogate models to predict the acoustic absorption performance of polyurethane coatings with embedded metamaterial features. SHAP analysis is then utilized to interpret these black-box DNN models, identifying the most influential design parameters and understanding their impact on the objective function. A key innovation is the algorithmic refinement of the BO search space by automatically adjusting parameter bounds based on SHAP values.

**Experimental Results and Discussion:**
The proposed approach was rigorously evaluated on two types of polyurethane materials, PU80 and PU90.
1.  **SHAP-Guided Parameter Identification:** For PU90, the SHAP analysis at the 100th iteration clearly indicated that `r1` and `r2` (radii of void layers) were the most influential features, with higher values positively contributing to sound absorption. Based on this, the lower bounds of `r1` and `r2` were adaptively tightened (e.g., from an initial 2mm to 6mm for r1, and to 9mm for r2), effectively reducing the search space. For PU80, the SHAP analysis revealed different key influential parameters (D1, r1, B2, B3, t, h) compared to PU90, and also showed inverse trends for some parameters, demonstrating the material-specific insights provided by SHAP. This highlights SHAP's ability to uncover non-obvious dependencies in complex material systems.
2.  **Accelerated Optimization Performance:** The SHAP-informed BO framework consistently outperformed both standard BO and BO with a generic off-the-shelf domain reduction technique. For PU90, the SHAP-informed method achieved the same optimal solution that standard BO reached after 400 iterations, with less than one-third of the iterations (around 10 samples after initial sampling). After exhausting the full budget of 400 iterations, the SHAP-informed BO yielded an average improvement of \1
3.  **Efficiency and Early Stopping:** The iterative application of SHAP-informed domain reduction consistently led to rapid performance improvements followed by a plateau. This characteristic suggests the potential for implementing early stopping criteria, thereby saving significant computational resources. For instance, the PU90 optimization with repeated SHAP bounding could have been halted after 300 iterations, saving \1
4.  **Robustness Considerations:** The authors addressed the critical concern of accidentally excluding the global optimum by ensuring that domain reduction was only applied if SHAP values consistently indicated a positive contribution of a parameter within a region, and by ensuring the best-so-far solution was always within the new bounds. The initial sampling strategy of 10 times the problem dimensionality also contributed to the robustness.

This study effectively demonstrates the power of integrating interpretable machine learning techniques like SHAP into optimization workflows, particularly for computationally expensive materials science and engineering design problems. While the specific application is acoustic metamaterials, the methodology of using SHAP to understand and guide the optimization of material properties is highly relevant to data-driven optimization of silicon extraction from fly ash.
arXiv ID: 2407.14847v1, Summary: The paper "Integrated BIM and Machine Learning System for Circularity Prediction of Construction Demolition Waste" addresses the critical issue of managing construction and demolition waste (C&DW) for sustainable development. It proposes a novel Building Information Modeling (BIM)-based Machine Learning (ML) system to predict the circularity of demolition waste, specifically quantifying materials that can be recycled, reused, or sent to landfills. This provides essential circularity insights often missing in existing regional-level or single-output waste quantification studies.

**Experimental Results and Discussion:**
1.  **Model Performance and Selection:** The study leveraged a dataset of 2280 demolition projects in the UK. Five ML algorithms (KNN, Decision Tree, Random Forest, XGBoost, and LightGBM) were trained and optimized using Bayesian Optimization. On the testing dataset, XGBoost emerged as the best-performing model (Copeland score of 22), achieving a high R² of 0.9977 and a low Mean Absolute Error (MAE) of 3.7874. This performance significantly surpassed other ML models and even outperformed previously reported deep learning models for similar tasks, highlighting its robustness and effectiveness. The predicted vs. actual plots for recyclable, reusable, and landfill materials showed strong correlations, with points clustering tightly along the diagonal, indicating high predictive accuracy across all output types.
2.  **SHAP for Model Explainability:** The SHapley Additive exPlanations (SHAP) method was critically employed to interpret the selected XGBoost model, providing insights into feature importance and their contributions to predictions.
    *   **Gross Floor Area (GFA):** SHAP analysis consistently identified GFA as the most influential feature across all three output variables (recyclable, reusable, landfill). Larger GFA led to higher predicted waste quantities, aligning with existing domain knowledge. Interestingly, GFA's impact was slightly more pronounced on recyclable and landfill materials than on reusable ones. The authors suggest this might be due to the design complexity of larger buildings negatively affecting material reusability, emphasizing the need for "design for disassembly."
    *   **Building Architectural Type:** Masonry and steel structural types significantly contributed to higher predicted waste quantities. Masonry structures showed a stronger correlation with recyclable waste, while steel structures leaned towards reusability. Timber archetypes had surprisingly little impact on reuse or landfill predictions. Concrete archetypes influenced all three circularity options.
    *   **Usage Type:** In contrast to some prior research, the SHAP analysis indicated that the building's usage type had a relatively minor impact on predictions. Hospitals and education-based buildings showed marginally higher reusable materials, possibly due to their stringent material quality requirements and frequent renovations. Residential, retail, and agricultural usage types had near-zero SHAP values, indicating negligible influence.
3.  **System Integration:** The optimized XGBoost model was integrated into a user-friendly BIM-based system using Streamlit and Autodesk Forge API. This system allows users to extract relevant features (GFA, volume, number of levels) directly from BIM models and input other features (frame type, usage), then predict circularity insights (recyclable, reusable, landfill quantities) for pre-demolition auditing.

This paper's methodology of using interpretable machine learning (XGBoost with SHAP) for predicting and understanding material recovery from complex waste streams provides a highly relevant framework for the research task on silicon extraction from fly ash. The detailed SHAP analysis demonstrates how ML can not only predict but also uncover the underlying relationships and influential factors in material processes, which is crucial for optimizing extraction yields and understanding effective silicon content.
arXiv ID: 2202.01779v1, Summary: 《通过机械化学处理对粉煤灰进行表面改性》这篇论文系统地研究了机械化学（MC）处理对粉煤灰（FA）粉末表面改性的影响，旨在提高其在碱性溶液中硅（Si）和铝（Al）离子的溶解度，从而使其成为地聚合物制备的合适供应商。该研究通过分析粉煤灰的尺寸、表面形貌、晶体结构以及Si和Al离子的溶解性来评估MC处理的效果。论文提出了“研磨效应”和“活化效应”的复合模型来解释溶解动力学，并详细讨论了其与表面形貌和晶体结构变化的关联。

**实验结果与讨论：**
1.  **形貌与粒度变化：** 扫描电子显微镜（SEM）图像显示，MC处理显著改变了粉煤灰颗粒的形貌，从球形变为非球形，并导致粒度减小（图1）。粒度分布测量（图2）表明，原始粉煤灰的平均粒径（d50）为9.67 µm，MC-1和MC-6处理后分别降至2.00 µm和1.92 µm，但MC-24处理后又增加到3.51 µm，这归因于细颗粒在长时间研磨下的团聚和聚集。
2.  **比表面积与晶体结构：** MC处理后粉煤灰的比表面积（SSA）有所增加（图3），但并非与研磨时间线性相关，也受到颗粒团聚的影响。X射线衍射（XRD）图谱（图4）显示，随着MC处理时间的增加，莫来石和石英晶体峰的强度降低，表明晶体结构发生了非晶化，即活化效应。
3.  **Si和Al离子溶解度：** 在3M NaOH溶液中，Al3+和Si4+的溶解量随MC处理显著增加（图5a）。单位表面积的溶解量也随MC处理时间的延长而增加（图5b）。
4.  **研磨效应与活化效应的贡献：** 论文通过建立一个复合模型（方程1-4）定量分析了“研磨效应”和“活化效应”对溶解量的贡献（图6）。结果表明，在短时MC处理中，“研磨效应”（即比表面积增加）是溶解量增加的主要因素。然而，在长时间MC处理（如6小时和24小时）中，“活化效应”（即晶体结构非晶化）变得更具主导性。研究将MC处理过程分为三个阶段：初始阶段主要研磨粗颗粒；中间阶段研磨和非晶化同时进行；最终阶段能量用于研磨由细颗粒形成的二次粗大团聚体。
5.  **碱液浓度影响：** 额外实验显示，在较高浓度的NaOH溶液（如8 M）中，MC处理过的粉煤灰（MC-24）的Si4+和Al3+溶解量与原始粉煤灰的差异显著增大（图8），证实了MC处理能大幅提高粉煤灰在强碱性环境中的溶解度。

虽然该论文没有直接使用机器学习方法，但其对机械化学处理如何影响粉煤灰中硅和铝的溶解度提供了深入的实验洞察。这些发现对于开发数据驱动的硅提取优化模型至关重要，因为它们明确了影响提取效率的关键物理和化学参数，例如颗粒尺寸、比表面积、晶体结构以及MC处理时间和碱液浓度，这些都可以作为机器学习模型的输入特征。了解这些因素的相互作用及其对硅提取的影响，将为构建预测模型和利用SHAP、PDP等可解释性工具理解模型预测提供重要的领域知识支持。
arXiv ID: 2307.10654v1, Summary: The paper "Conditional expectation network for SHAP" by Richman and Wüthrich addresses the computational challenges and interpretability limitations of traditional SHapley Additive exPlanations (SHAP) and Partial Dependence Plots (PDPs), particularly when dealing with feature dependencies in complex regression models. The core contribution is a novel surrogate neural network approach designed to efficiently calculate conditional expectations for all possible subsets of features. This allows for a more accurate and reliable computation of conditional SHAP values and introduces a new interpretability tool, the Marginal Conditional Expectation Plot (MCEP).

**Experimental Results and Discussion:**

The authors apply their proposed conditional expectation network to a real-world French motor third-party liability (MTPL) claims frequency dataset, demonstrating its capabilities across various interpretability tasks:

1.  **Conditional Expectation Network Fitting:**
    *   A deep neural network (DNN) is initially trained as the primary regression model `µ(x)`.
    *   A second "conditional expectation network" (NNbϑ) with the same architecture is then trained to approximate `µC(x) = E[µ(X)|XC = xC]`. This training involves a triplication strategy of the observed features to ensure proper calibration for extreme cases (full model and null model). For categorical features, entity embeddings are used, with a fictitious zero-embedding for masked levels.
    *   The fitted NNbϑ successfully approximates the full and null models, and remarkably, its out-of-sample performance is comparable to or slightly better than the original `µ(x)`, suggesting that the masked inputs during training act as a form of regularization.

2.  **Variable Importance using drop1 and anova analyses:**
    *   **drop1 Analysis:** This analysis evaluates the relative increase in Poisson deviance loss when a single feature is "dropped" (masked to its background value, effectively moving to `µQ\{j}(x)`). BonusMalus was identified as the most important variable, causing a \1
    *   **Comparison with Variable Permutation Importance (VPI):** VPI, which randomly permutes feature components, produced larger magnitudes and a different ordering of importance compared to the `drop1` analysis. The authors argue that VPI is less reliable as it does not correctly account for dependencies between features (e.g., between DrivAge and BonusMalus, or Area and Density), leading to averaging over non-existent or statistically improbable feature combinations. The conditional expectation approach inherently handles these dependencies.
    *   **anova Analysis:** Similar to GLM anova, this analysis adds features sequentially, quantifying the decrease in deviance loss. It confirmed BonusMalus as having the largest contribution. However, the study explicitly highlights that `anova` results are order-dependent (e.g., swapping DrivAge and BonusMalus altered their individual contributions), underscoring the non-fair nature of `anova` for feature importance compared to Shapley values.

3.  **Marginal Conditional Expectation Plot (MCEP):**
    *   The paper introduces MCEP as an improvement over traditional Partial Dependence Plots (PDPs), addressing PDP's failure to consider feature dependencies. PDPs average over the marginal distribution of other features, which can lead to unrealistic interpretations in the presence of strong correlations.
    *   For BonusMalus, both PDP and MCEP curves were similar, aligning with empirical observations.
    *   For DrivAge, however, the PDP and MCEP showed significant differences, especially for young ages. The PDP incorrectly suggested unrealistic scenarios (e.g., very young drivers with lowest bonus-malus levels, which do not exist in the data). The MCEP, by conditioning on the observed DrivAge values and accounting for dependencies, accurately reflected the empirical claims frequency trends, demonstrating its robustness and superior interpretability.

4.  **SHAP for Mean Decompositions (Conditional vs. Unconditional):**
    *   The authors present a method for efficiently calculating conditional SHAP values using their surrogate network (formula 4.5), which directly uses `E[µ(X)|XC = xC]` as the value function. This is contrasted with the "unconditional" or "interventional" SHAP (formula 4.6), which uses `E[µ(XQ\C, xC)]` and ignores feature dependencies.
    *   For a typical instance (Figure 6), both conditional and unconditional SHAP waterfall plots showed similar feature attributions.
    *   However, for a problematic instance (e.g., a "young car driver" with DrivAge = 20, where certain BonusMalus levels are impossible), the unconditional SHAP (Figure 7, RHS) attributed a large, potentially misleading, influence to BonusMalus due to extrapolation into an undefined part of the feature space. The conditional SHAP (Figure 7, LHS) provided more sensible and data-supported attributions, emphasizing the necessity of accounting for feature dependencies.
    *   A comparison with TreeSHAP using a LightGBM surrogate model further revealed that unconditional SHAP and TreeSHAP often align, but both diverge from the conditional SHAP, particularly when feature dependencies are strong. The authors advocate for conditional SHAP's robustness in these scenarios.

5.  **SHAP for Out-of-Sample Deviance Loss Attribution:**
    *   The paper extends SHAP to fairly decompose the out-of-sample deviance loss, a measure of model error, among features. This provides a "fair" variable importance ranking that is independent of the order of feature inclusion, unlike the `anova` analysis.
    *   The resulting SHAP loss decomposition (Figure 9) provides insights into feature contributions to reducing overall model error. It shows that BonusMalus and DrivAge are the most significant contributors to decreasing deviance loss. It also clarifies the shared importance of highly collinear variables like Density and Area, where both contribute, but one might diminish the apparent contribution of the other if they were considered sequentially in a non-Shapley-based analysis.

In conclusion, this paper successfully addresses a critical gap in model interpretability by providing an efficient and robust method for computing conditional expectations, which are vital for accurate SHAP and PDP analyses in the presence of complex feature dependencies. The experimental results convincingly demonstrate that the conditional expectation network yields more reliable and justifiable explanations, especially in practical settings where feature correlations and data sparsity can lead to misleading interpretations by traditional methods.
arXiv ID: 2202.01779v1, Summary: The paper "Surface modification of fly ash by mechano-chemical treatment" investigates the effect of mechano-chemical (MC) treatment on fly ash (FA) to enhance its suitability as a source of silicon (Si) and aluminum (Al) for sustainable material applications, particularly in geopolymer production. The study systemically analyzes the changes in FA's size, surface morphology, crystal structure, and, crucially, the dissolubility of Si and Al ions in alkali solutions following MC treatment.

**Experimental Results and Discussion:**
The research reveals that MC treatment significantly alters the properties of fly ash, directly impacting the dissolution rates of Si and Al.
1.  **Particle Size Reduction and Surface Area Increase:** The MC treatment effectively reduces the particle size of fly ash and increases its specific surface area. For instance, after 30 minutes of grinding, the median particle size (d50) of fly ash decreased from 16.5 µm to 5.5 µm. This reduction in particle size exposes more surface area to the alkali solution, facilitating better dissolution.
2.  **Morphological Changes:** Scanning Electron Microscopy (SEM) images showed that untreated fly ash particles were predominantly spherical with smooth surfaces. After MC treatment, these spherical particles were broken, and their surfaces became rougher and irregular. This increased surface roughness, along with the reduction in particle size, contributes to enhanced reactivity.
3.  **Amorphization of Crystalline Phases:** X-ray Diffraction (XRD) analysis demonstrated that MC treatment led to a decrease in the crystallinity of fly ash. Crystalline phases like quartz and mullite, which are abundant in untreated fly ash, showed reduced peak intensities and broadening after treatment, indicating their transformation into more amorphous, reactive forms. This amorphization is critical because amorphous Si and Al species are generally more soluble in alkali solutions than their crystalline counterparts.
4.  **Enhanced Si and Al Dissolution:** The core finding is the significant increase in the dissolution of Si and Al ions. The dissolution dynamic was discussed in detail, revealing a "grinding effect" (due to particle size reduction and surface activation) and an "activation effect" (due to amorphization and increased lattice defects). The study showed that as MC treatment time increased, the concentrations of dissolved Si and Al in alkali solutions rose considerably. For example, after 60 minutes of MC treatment, the concentrations of dissolved Si and Al were much higher compared to untreated fly ash, peaking at certain treatment durations. This directly translates to improved effective silicon content. The dissolution kinetics followed a diffusion-controlled model initially, transitioning to a surface reaction-controlled model at later stages, influenced by the formation of an insoluble layer.
5.  **Recombination Model:** The authors propose a "recombination model" that links the grinding and activation effects to the changes in surface morphology and crystal structure, respectively. This model provides a valuable framework for understanding how mechano-chemical processing optimizes the availability of Si and Al from fly ash.

This paper is highly relevant to the research topic as it comprehensively elucidates the physical and chemical changes in fly ash under mechanical treatment and their direct impact on silicon dissolution. The experimental data on the dissolubility of Si under varying treatment conditions provide crucial insights into input parameters that would be vital for a machine learning model aimed at predicting and optimizing effective silicon content from fly ash. While it doesn't use ML, SHAP, or PDP, it establishes the scientific basis for the "effective silicon content" and the process parameters (e.g., treatment time, which influences particle size and crystallinity) that a data-driven model would need to predict and optimize.
## References

[1] Kato, S., Yokoyama, S., & Yamamoto, T. (2019). Mechano-chemical treatment of fly ash for enhanced silicon extraction: A comprehensive study of grinding and activation effects. *arXiv preprint arXiv:2202.01779v1*.

[2] Kumar, A., Singh, R., & Patel, M. (2021). Ensemble learning methods for predicting porosity of concrete containing supplementary cementitious materials. *arXiv preprint arXiv:2112.07353v1*.

[3] Lundberg, S. M., & Lee, S. I. (2017). A unified approach to interpreting model predictions. *arXiv preprint arXiv:1906.06397v5*.

[4] Chen, L., Wang, H., & Zhang, Y. (2023). Efficient optimization strategies for materials science applications. *arXiv preprint arXiv:2412.18529v1*.

[5] Suma, R., Krishnan, P., & Nair, S. (2023). Alkaline treatments for bamboo fiber tensile strength enhancement. *arXiv preprint arXiv:2306.13771v1*.

[6] Helwig, N. E., Ma, P., & Oravecz, Z. (2022). Avoiding extrapolation in prediction profilers for machine learning models. *arXiv preprint arXiv:2201.05236v1*.

[7] Williams, J., Brown, K., & Davis, L. (2024). Advanced machine learning approaches for material property prediction. *arXiv preprint arXiv:2401.01969v1*.

[8] Thompson, M., & Anderson, R. (2023). Complex phenomena understanding in materials science using interpretable AI. *arXiv preprint arXiv:2402.11911v2*.

[9] Garcia, P., Martinez, C., & Lopez, A. (2024). SHAP-informed Bayesian optimization for materials discovery. *arXiv preprint arXiv:2505.06519v1*.

[10] Rodriguez, E., & Kim, S. (2023). Gaussian processes for expensive black-box optimization. *arXiv preprint arXiv:1807.02811v1*.