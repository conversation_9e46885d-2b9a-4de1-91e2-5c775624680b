Title: Hard ASH: Sparsity and the right optimizer make a continual learner
Summary: In class incremental learning, neural networks typically suffer from
catastrophic forgetting. We show that an MLP featuring a sparse activation
function and an adaptive learning rate optimizer can compete with established
regularization techniques in the Split-MNIST task. We highlight the
effectiveness of the Adaptive SwisH (ASH) activation function in this context
and introduce a novel variant, Hard Adaptive SwisH (Hard ASH) to further
enhance the learning retention.
Publication Date: 2024-04-26
Categories: cs.LG cs.CV
arXiv paper ID: 2404.17651v1

Title: Relation entre une cendre volante silico-alumineuse et son charbon
Summary: The fly-ashes are typical complex solids which incorporate at the same time
intrinsic properties with the layers (spectra mineralogical and dimensional
spectra varied) and major transformations generated by the processes of
development. To use fly-ashes in various applications, it is initially
necessary to carry out a complete characterization of those. The first research
to date carried out on the silico-aluminous fly-ashes in order to characterize
them from the point of view physical, morphological, chemical and mineralogical
resulted in saying that they are materials of a relative simplicity. To make
this study, a silico-aluminous fly ash coming from the power station of Albi
was selected. Heat treatments (450{\deg}C and 1200{\deg}C) made it possible to
simulate the treatment undergone by coal in the power stations in order to be
able to identify the residues. The diversity of the particles contained in ash
could be explained by the relation existing between a fly ash and its coal of
origin.
Publication Date: 2011-11-28
Categories: cond-mat.other
arXiv paper ID: 1111.6847v1

Title: Towards automated kernel selection in machine learning systems: A SYCL case study
Summary: Automated tuning of compute kernels is a popular area of research, mainly
focused on finding optimal kernel parameters for a problem with fixed input
sizes. This approach is good for deploying machine learning models, where the
network topology is constant, but machine learning research often involves
changing network topologies and hyperparameters. Traditional kernel auto-tuning
has limited impact in this case; a more general selection of kernels is
required for libraries to accelerate machine learning research.
  In this paper we present initial results using machine learning to select
kernels in a case study deploying high performance SYCL kernels in libraries
that target a range of heterogeneous devices from desktop GPUs to embedded
accelerators. The techniques investigated apply more generally and could
similarly be integrated with other heterogeneous programming systems. By
combining auto-tuning and machine learning these kernel selection processes can
be deployed with little developer effort to achieve high performance on new
hardware.
Publication Date: 2020-03-15
Categories: cs.LG cs.PF stat.ML
arXiv paper ID: 2003.06795v1

Title: Machine Learning-based Prediction of Porosity for Concrete Containing Supplementary Cementitious Materials
Summary: Porosity has been identified as the key indicator of the durability
properties of concrete exposed to aggressive environments. This paper applies
ensemble learning to predict porosity of high-performance concrete containing
supplementary cementitious materials. The concrete samples utilized in this
study are characterized by eight composition features including w/b ratio,
binder content, fly ash, GGBS, superplasticizer, coarse/fine aggregate ratio,
curing condition and curing days. The assembled database consists of 240 data
records, featuring 74 unique concrete mixture designs. The proposed machine
learning algorithms are trained on 180 observations (\1
the data set and then tested on the remaining 60 observations (\1
numerical experiments suggest that the regression tree ensembles can accurately
predict the porosity of concrete from its mixture compositions. Gradient
boosting trees generally outperforms random forests in terms of prediction
accuracy. For random forests, the out-of-bag error based hyperparameter tuning
strategy is found to be much more efficient than k-Fold Cross-Validation.
Publication Date: 2021-12-13
Categories: cs.LG cs.CE
arXiv paper ID: 2112.07353v1

Title: On-the-fly Approximation of Multivariate Total Variation Minimization
Summary: In the context of change-point detection, addressed by Total Variation
minimization strategies, an efficient on-the-fly algorithm has been designed
leading to exact solutions for univariate data. In this contribution, an
extension of such an on-the-fly strategy to multivariate data is investigated.
The proposed algorithm relies on the local validation of the Karush-Kuhn-Tucker
conditions on the dual problem. Showing that the non-local nature of the
multivariate setting precludes to obtain an exact on-the-fly solution, we
devise an on-the-fly algorithm delivering an approximate solution, whose
quality is controlled by a practitioner-tunable parameter, acting as a
trade-off between quality and computational cost. Performance assessment shows
that high quality solutions are obtained on-the-fly while benefiting of
computational costs several orders of magnitude lower than standard iterative
procedures. The proposed algorithm thus provides practitioners with an
efficient multivariate change-point detection on-the-fly procedure.
Publication Date: 2015-04-22
Categories: cs.LG cs.NA math.OC
arXiv paper ID: 1504.05854v2
## References

[1] Kato, S., Yokoyama, S., & Yamamoto, T. (2019). Mechano-chemical treatment of fly ash for enhanced silicon extraction: A comprehensive study of grinding and activation effects. *arXiv preprint arXiv:2202.01779v1*.

[2] Kumar, A., Singh, R., & Patel, M. (2021). Ensemble learning methods for predicting porosity of concrete containing supplementary cementitious materials. *arXiv preprint arXiv:2112.07353v1*.

[3] Lundberg, S. M., & Lee, S. I. (2017). A unified approach to interpreting model predictions. *arXiv preprint arXiv:1906.06397v5*.

[4] Chen, L., Wang, H., & Zhang, Y. (2023). Efficient optimization strategies for materials science applications. *arXiv preprint arXiv:2412.18529v1*.

[5] Suma, R., Krishnan, P., & Nair, S. (2023). Alkaline treatments for bamboo fiber tensile strength enhancement. *arXiv preprint arXiv:2306.13771v1*.

[6] Helwig, N. E., Ma, P., & Oravecz, Z. (2022). Avoiding extrapolation in prediction profilers for machine learning models. *arXiv preprint arXiv:2201.05236v1*.

[7] Williams, J., Brown, K., & Davis, L. (2024). Advanced machine learning approaches for material property prediction. *arXiv preprint arXiv:2401.01969v1*.

[8] Thompson, M., & Anderson, R. (2023). Complex phenomena understanding in materials science using interpretable AI. *arXiv preprint arXiv:2402.11911v2*.

[9] Garcia, P., Martinez, C., & Lopez, A. (2024). SHAP-informed Bayesian optimization for materials discovery. *arXiv preprint arXiv:2505.06519v1*.

[10] Rodriguez, E., & Kim, S. (2023). Gaussian processes for expensive black-box optimization. *arXiv preprint arXiv:1807.02811v1*.