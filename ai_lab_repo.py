import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from agents import *
from utils import save_text_to_markdown # Added import for saving to Markdown
from copy import copy
from common_imports import *
from mlesolver import MLESolver
from torch.backends.mkl import verbose
import re
import glob
from pathlib import Path

import argparse
import pickle

DEFAULT_LLM_BACKBONE = "gemini-2.5-flash" # Updated to a more stable Gemini model


class LaboratoryWorkflow:
    def __init__(self, research_topic, openai_api_key, openai_base_url=None, openai_model_name=None, max_steps=100, num_papers_lit_review=5, agent_model_backbone=f"{DEFAULT_LLM_BACKBONE}", notes=list(), human_in_loop_flag=None, compile_pdf=True, mlesolver_max_steps=3, papersolver_max_steps=5, args=None, author_name="Agent Laboratory"):
        # Added start_subtask for resuming workflow
        self.start_subtask = None
        """
        初始化实验室工作流
        @param research_topic: (str) 待探索的研究想法描述
        @param max_steps: (int) 每个阶段的最大步数，即计算容忍度预算
        @param num_papers_lit_review: (int) 文献综述中包含的论文数量
        @param agent_model_backbone: (str 或 dict) 用于智能体的模型骨干
        @param notes: (list) 智能体在任务中遵循的注意事项
        """

        self.notes = notes
        self.max_steps = max_steps
        self.compile_pdf = compile_pdf
        self.openai_api_key = openai_api_key
        self.openai_base_url = openai_base_url
        self.openai_model_name = openai_model_name
        self.research_topic = research_topic
        self.model_backbone = agent_model_backbone
        self.num_papers_lit_review = num_papers_lit_review
        self.args = args  # Store args for later use
        self.author_name = author_name # Store author_name

        self.print_cost = True
        self.cost = 0.0  # Initialize cost tracking
        self.review_override = True # should review be overridden?
        self.review_ovrd_steps = 0 # review steps so far
        self.arxiv_paper_exp_time = 3
        self.reference_papers = list()

        ##########################################
        ####### COMPUTE BUDGET PARAMETERS ########
        ##########################################
        self.num_ref_papers = 1
        self.review_total_steps = 0 # num steps to take if overridden
        self.arxiv_num_summaries = num_papers_lit_review  # Use the parameter passed from command line
        self.mlesolver_max_steps = mlesolver_max_steps
        self.papersolver_max_steps = papersolver_max_steps

        self.phases = [
            ("literature review", ["literature review"]),
            # ("visualization", ["visualization"]),  # 跳过visualization阶段，避免重复执行visualization_utils.py
            ("plan formulation", ["plan formulation"]),
            ("experimentation", ["data preparation", "running experiments"]),
            ("results interpretation", ["results interpretation", "report writing", "report refinement"]),
        ]
        self.phase_status = dict()
        for phase, subtasks in self.phases:
            for subtask in subtasks:
                self.phase_status[subtask] = False

        self.phase_models = dict()
        if type(agent_model_backbone) == str:
            for phase, subtasks in self.phases:
                for subtask in subtasks:
                    self.phase_models[subtask] = agent_model_backbone
        elif type(agent_model_backbone) == dict:
            # todo: check if valid
            self.phase_models = agent_model_backbone


        self.human_in_loop_flag = human_in_loop_flag

        self.statistics_per_phase = {
            "literature review":      {"time": 0.0, "steps": 0.0,},
            "plan formulation":       {"time": 0.0, "steps": 0.0,},
            "data preparation":       {"time": 0.0, "steps": 0.0,},
            "running experiments":    {"time": 0.0, "steps": 0.0,},
            "results interpretation": {"time": 0.0, "steps": 0.0,},
            "report writing":         {"time": 0.0, "steps": 0.0,},
            "report refinement":      {"time": 0.0, "steps": 0.0,},
        }

        self.save = True
        self.verbose = True
        self.reviewers = ReviewersAgent(model=self.model_backbone, notes=self.notes, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)
        self.phd = PhDStudentAgent(model=self.model_backbone, notes=self.notes, max_steps=self.max_steps, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)
        self.postdoc = PostdocAgent(model=self.model_backbone, notes=self.notes, max_steps=self.max_steps, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)
        self.professor = ProfessorAgent(model=self.model_backbone, notes=self.notes, max_steps=self.max_steps, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)
        self.ml_engineer = MLEngineerAgent(model=self.model_backbone, notes=self.notes, max_steps=self.max_steps, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)

        # Initialize dialogue history for saving
        self.dialogue_history = []
        self.current_phase_dialogues = []

    def set_model(self, model):
        self.set_agent_attr("model", model)
        self.reviewers.model = model

    def save_state(self, phase):
        """
        保存阶段状态
        @param phase: (str) 阶段字符串
        @return: None
        """
        phase = phase.replace(" ", "_")
        save_dir = "outputs/state_saves"
        os.makedirs(save_dir, exist_ok=True)
        with open(os.path.join(save_dir, f"{phase}.pkl"), "wb") as f:
            pickle.dump(self, f)
        
        # Save dialogues for this phase
        if len(self.current_phase_dialogues) > 0:
            self.save_phase_dialogues_to_file(phase)

    def save_dialogue(self, speaker, dialogue_content, phase):
        """
        Save dialogue content with timestamp and speaker information
        @param speaker: (str) who is speaking (e.g., "Postdoc", "PhD", "ML Engineer")
        @param dialogue_content: (str) the actual dialogue content
        @param phase: (str) current phase of the research
        @return: None
        """
        import datetime
        
        dialogue_entry = {
            "timestamp": datetime.datetime.now().isoformat(),
            "phase": phase,
            "speaker": speaker,
            "content": dialogue_content
        }
        
        self.current_phase_dialogues.append(dialogue_entry)
        self.dialogue_history.append(dialogue_entry)
        
        if self.verbose:
            print(f"[DIALOGUE SAVED] {speaker} in {phase}: {dialogue_content[:100]}...")
    
    def save_all_dialogues_to_file(self, filename=None):
        """
        将所有收集到的对话保存到文件
        @param filename: (str) 可选文件名，默认为带时间戳的文件名
        @return: None
        """
        import datetime
        import json
        
        if filename is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"dialogue_history_{timestamp}.json"
        
        filepath = os.path.join("./research_dir", filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.dialogue_history, f, ensure_ascii=False, indent=2)
            print(f"所有对话已保存到: {filepath}")
            print(f"总共保存了 {len(self.dialogue_history)} 条对话记录")
        except Exception as e:
            print(f"保存对话时出错: {str(e)}")
    
    def save_phase_dialogues_to_file(self, phase, filename=None):
        """
        Save dialogues from current phase to a separate file
        @param phase: (str) phase name
        @param filename: (str) optional filename
        @return: None
        """
        import datetime
        import json
        
        if filename is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"dialogue_{phase.replace(' ', '_')}_{timestamp}.json"
        
        filepath = os.path.join("./research_dir", filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.current_phase_dialogues, f, ensure_ascii=False, indent=2)
            print(f"{phase} 阶段的对话已保存到: {filepath}")
            print(f"该阶段保存了 {len(self.current_phase_dialogues)} 条对话记录")
            
            # Clear current phase dialogues for next phase
            self.current_phase_dialogues = []
        except Exception as e:
            print(f"保存 {phase} 阶段对话时出错: {str(e)}")

    def set_agent_attr(self, attr, obj):
        """
        为所有智能体设置属性
        @param attr: (str) 智能体属性
        @param obj: (object) 对象属性
        @return: None
        """
        setattr(self.phd, attr, obj)
        setattr(self.postdoc, attr, obj)
        setattr(self.professor, attr, obj)
        setattr(self.ml_engineer, attr, obj)

    def reset_agents(self):
        """
        Reset all agent states
        @return: None
        """
        self.phd.reset()
        self.postdoc.reset()
        self.professor.reset()
        self.ml_engineer.reset()

    def perform_research(self):
        """
        遍历所有研究阶段
        @return: None
        """
        skip_to_start_subtask = self.start_subtask is not None
        
        for phase, subtasks in self.phases:
            # Only start timing if we are not skipping
            # Also, print "Beginning phase" only if not skipping or if it's the actual starting phase
            if not skip_to_start_subtask or any(s.replace(" ", "_") == self.start_subtask for s in subtasks):
                phase_start_time = time.time()  # Start timing the phase
                if self.verbose: print(f"{'*'*50}\nBeginning phase: {phase}\n{'*'*50}")
            
            for subtask in subtasks:
                if skip_to_start_subtask:
                    if subtask.replace(" ", "_") == self.start_subtask:
                        skip_to_start_subtask = False # Found the starting point, stop skipping
                        if self.verbose: print(f"--- [INFO] Resuming from subtask: {subtask}")
                    else:
                        if self.verbose: print(f"--- [INFO] Skipping subtask: {subtask}")
                        continue # Skip this subtask

                # Original logic for executing subtasks
                if self.verbose: print(f"{'&'*30}\nBeginning subtask: {subtask}\n{'&'*30}")
                if type(self.phase_models) == dict:
                    if subtask in self.phase_models:
                        self.set_model(self.phase_models[subtask])
                    else: self.set_model(f"{DEFAULT_LLM_BACKBONE}")
                
                # Check if subtask needs to be performed
                if (subtask not in self.phase_status or not self.phase_status[subtask]):
                    return_to_exp_phase = False  # Initialize the variable

                    if subtask == "literature review":
                        repeat = True
                        while repeat: repeat = self.literature_review()
                        self.phase_status[subtask] = True

                    elif subtask == "plan formulation":
                        repeat = True
                        while repeat: repeat = self.plan_formulation()
                        self.phase_status[subtask] = True
                    elif subtask == "data preparation":
                        repeat = True
                        while repeat: repeat = self.data_preparation()
                        self.phase_status[subtask] = True
                    elif subtask == "running experiments":
                        repeat = True
                        while repeat: repeat = self.running_experiments()
                        self.phase_status[subtask] = True
                    elif subtask == "results interpretation":
                        repeat = True
                        while repeat: repeat = self.results_interpretation()
                        self.phase_status[subtask] = True
                    elif subtask == "report writing":
                        repeat = True
                        while repeat: repeat = self.report_writing()
                        self.phase_status[subtask] = True
                    elif subtask == "report refinement":
                        return_to_exp_phase = self.report_refinement()

                        if not return_to_exp_phase:
                            if self.save: self.save_state(subtask)
                            return

                    # Only execute the following if we need to return to experiment phase
                    if return_to_exp_phase:
                        self.set_agent_attr("second_round", return_to_exp_phase)
                        self.set_agent_attr("prev_report", copy(self.phd.report))
                        self.set_agent_attr("prev_exp_results", copy(self.phd.exp_results))
                        self.set_agent_attr("prev_results_code", copy(self.phd.results_code))
                        self.set_agent_attr("prev_interpretation", copy(self.phd.interpretation))

                        self.phase_status["plan formulation"] = False
                        self.phase_status["data preparation"] = False
                        self.phase_status["running experiments"] = False
                        self.phase_status["results interpretation"] = False
                        self.phase_status["report writing"] = False
                        self.phase_status["report refinement"] = False
                        self.perform_research()
                if self.save: self.save_state(subtask)
                # Calculate and print the duration of the phase
                phase_end_time = time.time()
                phase_duration = phase_end_time - phase_start_time
                print(f"Subtask '{subtask}' completed in {phase_duration:.2f} seconds.")
                self.statistics_per_phase[subtask]["time"] = phase_duration

        # After all phases, specifically after report refinement, check for --save-docx
        # Ensure this runs outside the subtask loop, after all phases are completed or at the very end of perform_research.
        # The check for 'report refinement' completion ensures we have a final paper to save.
        if hasattr(self, 'args') and self.args and hasattr(self.args, 'save_docx') and self.args.save_docx and self.phase_status.get("report refinement", False):
            print("\nAttempting to save the report as Markdown...")
            # Try to get the final report from different possible sources
            final_paper_text = None
            
            # First try to get from self.phd.report
            if hasattr(self, 'phd') and hasattr(self.phd, 'report') and self.phd.report:
                final_paper_text = self.phd.report
            # Then try from solver if it exists
            elif hasattr(self, 'solver') and self.solver and hasattr(self.solver, 'paper_lines') and self.solver.paper_lines:
                final_paper_text = "\n".join(self.solver.paper_lines)
            
            if final_paper_text:
                # Determine save path
                markdown_save_path = os.path.join("./research_dir", "final_report.md")
                
                # Ensure the directory exists
                if not os.path.exists(os.path.dirname(markdown_save_path)):
                    os.makedirs(os.path.dirname(markdown_save_path), exist_ok=True)
                
                try:
                    from utils import save_text_to_markdown
                    if save_text_to_markdown(final_paper_text, markdown_save_path):
                        print(f"Final report successfully saved to {markdown_save_path}")
                    else:
                        print(f"Failed to save final report to {markdown_save_path}")
                except ImportError:
                    # Fallback to simple file writing if utils is not available
                    try:
                        with open(markdown_save_path, 'w', encoding='utf-8') as f:
                            f.write(final_paper_text)
                        print(f"Final report successfully saved to {markdown_save_path}")
                    except Exception as e:
                        print(f"Failed to save final report: {e}")
            else:
                print("Could not retrieve final paper content to save as Markdown.")


    def report_refinement(self):
        """
        Perform report refinement phase
        @return: (bool) whether to repeat the phase
        """
        reviews = self.reviewers.inference(self.phd.plan, self.phd.report)
        print("Reviews:", reviews)
        if self.human_in_loop_flag["report refinement"]:
            print(f"Provided are reviews from a set of three reviewers: {reviews}")
            input("Would you like to be completed with the project or should the agents go back and improve their experimental results?\n (y) for go back (n) for complete project: ")
        else:
            review_prompt = f"Provided are reviews from a set of three reviewers: {reviews}. Would you like to be completed with the project or do you want to go back to the planning phase and improve your experiments?\n Type y and nothing else to go back, type n and nothing else for complete project."
            self.phd.phases.append("report refinement")
            if self.review_override:
                if self.review_total_steps == self.review_ovrd_steps:
                    response = "n"
                else:
                    response = "y"
                    self.review_ovrd_steps += 1
            else:
                response = self.phd.inference(
                    research_topic=self.research_topic, phase="report refinement", feedback=review_prompt, step=0)
            if len(response) == 0:
                raise Exception("Model did not respond")
            response = response.lower().strip()[0]
            if response == "n":
                if verbose: print("*"*40, "\n", "REVIEW COMPLETE", "\n", "*"*40)
                return False
            elif response == "y":
                self.set_agent_attr("reviewer_response", f"Provided are reviews from a set of three reviewers: {reviews}.")
                return True
            else: raise Exception("Model did not respond")

    def report_writing(self):
        """
        执行报告撰写阶段
        @return: (bool) 是否重复该阶段
        """
        # experiment notes
        report_notes = [_note["note"] for _note in self.ml_engineer.notes if "report writing" in _note["phases"]]
        report_notes = f"Notes for the task objective: {report_notes}\n" if len(report_notes) > 0 else ""
        # instantiate mle-solver
        from papersolver import PaperSolver
        self.reference_papers = []

        # --- 图片处理逻辑（假设）---
        # 假设在实验运行后，图片路径列表存储在 self.phd.generated_figures_list
        # 路径应相对于 ./research_dir/
        # 例如: self.phd.generated_figures_list = ["silicon_analysis_plots_20230101_120000/target_distribution.png", "silicon_analysis_plots_20230101_120000/advanced_correlation_matrix.png"]
        # 在实际应用中，您需要确保此列表被正确填充。
        # 为了演示，如果属性不存在，则使用空列表。
        generated_images_list = getattr(self.phd, 'generated_figures_list', [])
        if not generated_images_list and self.verbose:
            print("⚠️ [报告撰写] 未找到生成的图片列表 (self.phd.generated_figures_list)。图片可能不会被插入。")
        elif generated_images_list and self.verbose:
            print(f"ℹ️ [报告撰写] 将尝试使用以下图片列表插入报告: {generated_images_list}")
        # --- 结束图片处理逻辑 ---

        solver = PaperSolver(
            notes=report_notes, 
            max_steps=self.papersolver_max_steps, 
            plan=self.phd.plan, 
            exp_code=self.phd.results_code, 
            exp_results=self.phd.exp_results, 
            insights=self.phd.interpretation, 
            lit_review=self.phd.lit_review, 
            ref_papers=self.reference_papers, 
            topic=self.research_topic, 
            openai_api_key=self.openai_api_key, 
            openai_base_url=self.openai_base_url, 
            openai_model_name=self.openai_model_name, 
            llm_str=self.model_backbone["report writing"], 
            compile_pdf=self.compile_pdf, 
            author_name=self.author_name,
            generated_images=generated_images_list, # 传递图片列表
            existing_report_path=os.path.join("./research_dir", "research_paper.md") # Assuming this is the path to the existing report
        )
        # run initialization for solver
        solver.initial_solve()
        # run solver for N mle optimization steps
        for _ in range(self.papersolver_max_steps):
            solver.solve()
        # get best report results
        report = "\n".join(solver.best_report[0][0])
        score = solver.best_report[0][1]

        # 应用智能LLM后处理来修复格式问题和生成缺失内容
        report = solver.post_process_report(report)

        if self.verbose:
            print("✅ [报告撰写] 已应用智能LLM后处理：")
            print("  - 🧠 LLM智能生成缺失章节内容")
            print("  - 🖼️ LLM智能插入图片到相关章节")
            print("  - 🔧 LLM智能优化格式和数学公式")
            print("  - 📚 自动添加参考文献")
            print("  - 🎨 专业学术格式优化")

        if self.verbose: print(f"Report writing completed, reward function score: {score}")
        if self.human_in_loop_flag["report writing"]:
            retry = self.human_in_loop("report writing", report)
            if retry: return retry
        self.set_agent_attr("report", report)
        readme = self.professor.generate_readme()
        save_to_file("./research_dir", "readme.md", readme)
        save_to_file("./research_dir", "report.txt", report)
        self.reset_agents()
        return False

    def results_interpretation(self):
        """
        执行结果解释阶段
        @return: (bool) 是否重复该阶段
        """
        max_tries = self.max_steps
        dialogue = f"Analysis results:\n{self.results_df}"
        # iterate until max num tries to complete task is exhausted
        for _i in range(max_tries):
            print(f"🔬 [Step {_i+1}] Postdoc analyzing results...")
            resp = self.postdoc.inference(self.research_topic, "results interpretation", feedback=dialogue, step=_i)
            print("="*80)
            print("🧑‍🔬 POSTDOC RESPONSE:")
            print("="*80)
            print(resp)
            print("="*80)
            dialogue = str()
            if "```DIALOGUE" in resp:
                dialogue = extract_prompt(resp, "DIALOGUE")
                dialogue = f"The following is dialogue produced by the postdoctoral researcher: {dialogue}"
                print("#"*40)
                print("📝 POSTDOC DIALOGUE EXTRACTED:")
                print("#"*40)
                print(dialogue)
                print("#"*40)
                # Save dialogue
                self.save_dialogue("Postdoc", dialogue, "results interpretation")
            if "```INTERPRETATION" in resp:
                interpretation = extract_prompt(resp, "INTERPRETATION")
                if self.human_in_loop_flag["results interpretation"]:
                    retry = self.human_in_loop("results interpretation", interpretation)
                    if retry: return retry
                self.set_agent_attr("interpretation", interpretation)
                # reset agent state
                self.reset_agents()
                self.statistics_per_phase["results interpretation"]["steps"] = _i
                return False
            print(f"🎓 [Step {_i+1}] PhD Student responding...")
            resp = self.phd.inference(self.research_topic, "results interpretation", feedback=dialogue, step=_i)
            print("="*80)
            print("🎓 PHD STUDENT RESPONSE:")
            print("="*80)
            print(resp)
            print("="*80)
            dialogue = str()
            if "```DIALOGUE" in resp:
                dialogue = extract_prompt(resp, "DIALOGUE")
                dialogue = f"The following is dialogue produced by the PhD student: {dialogue}"
                print("#"*40)
                print("📝 PHD DIALOGUE EXTRACTED:")
                print("#"*40)
                print(dialogue)
                print("#"*40)
                # Save dialogue
                self.save_dialogue("PhD", dialogue, "results interpretation")
        raise Exception("Max tries during phase: Results Interpretation")

    def running_experiments(self):
        """
        Perform running experiments phase by directly importing and calling the modularized analysis pipeline.
        @return: (bool) whether to repeat the phase
        """
        import subprocess
        import sys
        
        # Hardcoded path to the run_analysis.py script
        script_path = "src/run_analysis.py"
        
        try:
            # Execute the script as a separate process
            result = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True,
                check=True
            )
            
            # The output of the script will be in result.stdout
            exp_results = result.stdout
            print("✅ Analysis script executed successfully.")
            print(exp_results)
            
            # Parse plot paths from the script output
            plot_paths_start_idx = exp_results.find("---PLOT_PATHS_START---")
            plot_paths_end_idx = exp_results.find("---PLOT_PATHS_END---")
            
            if plot_paths_start_idx != -1 and plot_paths_end_idx != -1:
                plot_paths_str = exp_results[plot_paths_start_idx + len("---PLOT_PATHS_START---"):plot_paths_end_idx].strip()
                parsed_plot_paths = {}
                for line in plot_paths_str.split('\n'):
                    if ':' in line:
                        key, value = line.split(':', 1)
                        parsed_plot_paths[key.strip()] = value.strip()
                self.phd.generated_figures_list = list(parsed_plot_paths.values())
                print(f"✅ Collected {len(self.phd.generated_figures_list)} plot paths from analysis script output.")
            else:
                print("⚠️ Could not find plot paths in analysis script output.")
                self.phd.generated_figures_list = []

            # The 'code' can be the script itself
            with open(script_path, 'r', encoding='utf-8') as f:
                code = f.read()

        except subprocess.CalledProcessError as e:
            error_msg = f"An error occurred during the execution of the analysis pipeline:\n{e.stderr}"
            print(f"❌ {error_msg}")
            exp_results = error_msg
            code = "# Analysis pipeline execution failed."
        except FileNotFoundError:
            error_msg = f"Error: The script at {script_path} was not found."
            print(f"❌ {error_msg}")
            exp_results = error_msg
            code = "# Analysis script not found."

        self.set_agent_attr("exp_results", exp_results)
        self.set_agent_attr("results_code", code)
        # Parse the results_df from the script's output by finding the JSON file path.
        try:
            summary_path_line = [line for line in exp_results.split('\n') if "完整分析总结已保存到:" in line]
            if summary_path_line:
                summary_path = summary_path_line[0].split("完整分析总结已保存到: ")[1].strip()
                with open(summary_path, 'r', encoding='utf-8') as f:
                    results_data = json.load(f)
                # Convert the loaded data (list of dicts) to a DataFrame, then to a JSON string
                self.results_df = pd.DataFrame(results_data).to_json(orient='records')
                print(f"✅ Successfully parsed results_df from {summary_path}")
            else:
                print("⚠️ Could not find the summary JSON path in the script output.")
                self.results_df = "Could not find summary JSON path in script output."
        except Exception as e:
            print(f"⚠️ An error occurred while parsing results_df from the JSON file: {e}")
            self.results_df = f"Error parsing results_df: {e}"
        self.reset_agents()
        return False

    def data_preparation(self):
        """
        Data preparation phase with enhanced retry mechanism for empty code blocks
        """
        if self.verbose: print("^"*50, "data preparation", "^"*50)
        
        # PhD Student response
        phd_context = self.phd.context("data preparation")
        if isinstance(phd_context, tuple):
            phd_context = "".join(phd_context)
        phd_resp = self.phd.inference(self.research_topic, "data preparation", 0,
                                        phd_context +
                                        self.phd.phase_prompt("data preparation"))
        phd_dialogue = ""
        if phd_resp is not None:
            phd_dialogue = f"\nThe following is dialogue produced by the PhD Student: {phd_resp}\n"
            if self.verbose: print("#" * 40, f"\nThe following is dialogue produced by the PhD Student: {phd_resp}", "#" * 40, "\n")
            # Save dialogue
            self.save_dialogue("PhD", phd_resp, "data preparation")
        
        # ML Engineer response
        ml_context = self.ml_engineer.context("data preparation")
        if isinstance(ml_context, tuple):
            ml_context = "".join(ml_context)
        ml_resp = self.ml_engineer.inference(self.research_topic, "data preparation", 0,
                                            ml_context +
                                            self.ml_engineer.phase_prompt("data preparation"))
        ml_dialogue = ""
        ml_feedback = ""
        ml_command = ""
        
        if ml_resp is not None:
            ml_dialogue = f"\nThe following is dialogue produced by the ML Engineer: {ml_resp}\n"
            if self.verbose: print("#" * 40, f"\nThe following is dialogue produced by the ML Engineer: {ml_resp}", "#" * 40, "\n")
            # Save dialogue
            self.save_dialogue("ML Engineer", ml_resp, "data preparation")
        
        # Extract and execute code
        if ml_resp and "```python" in ml_resp:
            code = extract_prompt(ml_resp, "python")
            if code and code.strip():  # Check if code is not empty
                # Safely handle dataset_code concatenation
                dataset_code = self.ml_engineer.dataset_code or ""
                full_code = dataset_code + "\n" + code if dataset_code else code
                
                code_resp = execute_code(full_code, timeout=120)
                ml_command = f"Code produced by the ML agent:\n{full_code}"
                ml_feedback += f"\nCode Response: {code_resp}\n"
                if self.verbose: print("!"*100, "\n", f"CODE RESPONSE: {code_resp}")
            else:
                if self.verbose: print("Warning: SUBMIT_CODE was found but the content was empty. Cannot execute empty code.")
                ml_feedback += "\n[CODE EXECUTION ERROR] SUBMIT_CODE was empty.\n"
                if self.verbose: print("!"*100, "\n", f"CODE RESPONSE: [CODE EXECUTION ERROR] SUBMIT_CODE was empty.")
        
        # Handle HuggingFace search
        if ml_resp and "```SEARCH_HF" in ml_resp:
            hf_query = extract_prompt(ml_resp, "SEARCH_HF")
            if hf_query and hf_query.strip():
                hf_resp = search_huggingface(hf_query)
                ml_feedback += f"\nHuggingFace Search Response: {hf_resp}\n"
                if self.verbose: print("HuggingFace Search Response:", hf_resp)
            else:
                if self.verbose: print("Warning: Could not extract SEARCH_HF query or query was empty from ML Engineer response.")

        # Update cost tracking
        self.cost += self.phd.cost + self.ml_engineer.cost
        if self.verbose: print(f"Current experiment cost = ${self.cost}, ** Approximate values, may not reflect true cost")

        # Update communication history
        self.phd.prev_comm += phd_dialogue + ml_dialogue + ml_command + ml_feedback
        self.ml_engineer.prev_comm += phd_dialogue + ml_dialogue + ml_command + ml_feedback

        # Save state
        self.save_state("data preparation")
        
        # Continue to next phase if we have any progress, even with errors
        return False

    def plan_formulation(self):
        """
        Perform plan formulation phase
        @return: (bool) whether to repeat the phase
        """
        max_tries = self.max_steps
        dialogue = str()
        # iterate until max num tries to complete task is exhausted
        for _i in range(max_tries):
            # inference postdoc to
            resp = self.postdoc.inference(self.research_topic, "plan formulation", feedback=dialogue, step=_i)
            if self.verbose: print("Postdoc: ", resp, "\n~~~~~~~~~~~")
            dialogue = str()

            if "```DIALOGUE" in resp:
                dialogue = extract_prompt(resp, "DIALOGUE")
                dialogue = f"The following is dialogue produced by the postdoctoral researcher: {dialogue}"
                if self.verbose: print("#"*40, "\n", "Postdoc Dialogue:", dialogue, "\n", "#"*40)
                # Save dialogue
                self.save_dialogue("Postdoc", dialogue, "plan formulation")

            if "```PLAN" in resp:
                plan = extract_prompt(resp, "PLAN")
                if self.human_in_loop_flag["plan formulation"]:
                    retry = self.human_in_loop("plan formulation", plan)
                    if retry: return retry
                self.set_agent_attr("plan", plan)
                # reset agent state
                self.reset_agents()
                self.statistics_per_phase["plan formulation"]["steps"] = _i
                return False

            resp = self.phd.inference(self.research_topic, "plan formulation", feedback=dialogue, step=_i)
            if self.verbose: print("PhD Student: ", resp, "\n~~~~~~~~~~~")

            dialogue = str()
            if "```DIALOGUE" in resp:
                dialogue = extract_prompt(resp, "DIALOGUE")
                dialogue = f"The following is dialogue produced by the PhD student: {dialogue}"
                if self.verbose: print("#"*40, "\n", "PhD Dialogue:", dialogue, "#"*40, "\n")
                # Save dialogue
                self.save_dialogue("PhD", dialogue, "plan formulation")
        raise Exception("Max tries during phase: Plan Formulation")

    def literature_review(self):
        """
        Perform literature review phase
        @return: (bool) whether to repeat the phase
        """
        # If num_papers_lit_review is 0, skip this phase entirely
        if self.arxiv_num_summaries == 0:
            print("Skipping literature review phase as num_papers_lit_review is 0.")
            return False # Do not repeat phase

        # Initialize search engines
        arx_eng = ArxivSearch()
        perplexity_eng = None

        # Try to initialize Perplexity with optimal configuration
        perplexity_config = None
        try:
            from perplexity_search import PerplexitySearch
            from perplexity_config import setup_perplexity_for_agent_lab

            perplexity_api_key = os.getenv('PERPLEXITY_API_KEY')
            if perplexity_api_key:
                # Setup optimal configuration
                perplexity_config = setup_perplexity_for_agent_lab(self.research_topic, budget=15.0)
                if "error" not in perplexity_config:
                    perplexity_eng = PerplexitySearch(perplexity_api_key)
                    print("✅ Perplexity AI initialized as PRIMARY literature review engine")
                    print("📚 Enhanced with optimal configuration for your research topic")
                    print(f"🎯 Strategy: {perplexity_config['strategy']['primary_approach']}")
                    print(f"💰 Estimated cost: ${perplexity_config['strategy']['estimated_cost']:.3f}")
                else:
                    print(f"❌ Perplexity configuration error: {perplexity_config['error']}")
                    perplexity_eng = None
            else:
                print("⚠️  PERPLEXITY_API_KEY not found. Using ArXiv search only.")
                print("💡 For enhanced literature review, set PERPLEXITY_API_KEY environment variable.")
                perplexity_eng = None
        except ImportError as e:
            print("❌ Perplexity search not available. Using ArXiv search only.")
            print(f"💡 Install perplexity dependencies: {e}")
            perplexity_eng = None

        max_tries = self.max_steps * 5 # lit review often requires extra steps

        # Provide initial guidance based on available tools
        initial_guidance = ""
        if perplexity_eng:
            initial_guidance = (
                "🚀 RECOMMENDED: Start with PERPLEXITY_REVIEW command for comprehensive literature analysis. "
                "Perplexity AI provides access to multiple academic databases with AI-powered insights and automatic citations. "
                "This is more efficient and comprehensive than traditional ArXiv searches."
            )
        else:
            initial_guidance = (
                "📚 Using ArXiv search for literature review. "
                "For enhanced results, consider setting up Perplexity API key for AI-powered literature analysis."
            )

        # get initial response from PhD agent
        resp = self.phd.inference(self.research_topic, "literature review", feedback=initial_guidance, step=0, temp=0.8)
        if self.verbose: print(resp, "\n~~~~~~~~~~~")
        # iterate until max num tries to complete task is exhausted
        for _i in range(max_tries):
            feedback = str()

            # grab summary of papers from arxiv and perplexity
            if "```SUMMARY" in resp:
                query = extract_prompt(resp, "SUMMARY")
                if query:
                    # Use ArXiv search
                    try:
                        papers = arx_eng.find_papers_by_str(query, N=self.arxiv_num_summaries)
                        
                        # Save the search results to a markdown file for each query
                        # Sanitize the query to create a valid filename, allowing Chinese characters
                        filename_query = re.sub(r'[^\w\s\-\.\u4e00-\u9fa5]', '', query.strip()).replace(' ', '_')
                        
                        # Fallback for empty or invalid filenames
                        if not filename_query:
                            filename_query = "search_results"

                        import datetime
                        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"SUMMARY_{filename_query}_{timestamp}.md"
                        filepath = os.path.join("./research_dir", filename)
                        papers_str = papers if isinstance(papers, str) else str(papers)
                        
                        # Corrected argument order for save_text_to_markdown
                        if save_text_to_markdown(papers_str, filepath):
                            if self.verbose:
                                print(f"✅ Search results for query '{query.strip()}' saved to: {filepath}")
                        # The save_text_to_markdown function will print its own error message on failure.

                    except Exception as e:
                        print(f"Warning: ArXiv search failed due to a network error: {e}")
                        print("Skipping this search query and continuing.")
                        papers = f"ArXiv search failed for query: {query}. Please try a different query."
                else:
                    print("Warning: Could not extract SUMMARY query from LLM response for literature review.")
                    papers = "Could not retrieve papers as the SUMMARY query was empty." # Provide some feedback
                feedback = f"You requested papers related to the query '{query}', here was the response\n{papers}"

            # grab full text from arxiv ID
            elif "```FULL_TEXT" in resp:
                query = extract_prompt(resp, "FULL_TEXT")
                # expiration timer so that paper does not remain in context too long
                arxiv_paper = f"```EXPIRATION {self.arxiv_paper_exp_time}\n" + arx_eng.retrieve_full_paper_text(query) + "```"
                feedback = arxiv_paper

            # if add paper, extract and add to lit review, provide feedback
            elif "```ADD_PAPER" in resp:
                query = extract_prompt(resp, "ADD_PAPER")
                feedback, text = self.phd.add_review(query, arx_eng)
                if len(self.reference_papers) < self.num_ref_papers:
                    self.reference_papers.append(text)

            # Enhanced Perplexity literature review generation - PRIMARY METHOD
            elif "```PERPLEXITY_REVIEW" in resp and perplexity_eng:
                feedback = "Perplexity has been disabled. Please use the 'SUMMARY' command to search on ArXiv."

            # Fallback message if Perplexity is not available
            elif "```PERPLEXITY_REVIEW" in resp and not perplexity_eng:
                feedback = "❌ Perplexity AI is not available. Please use SUMMARY command for ArXiv search or set up Perplexity API key."

            # completion condition
            if len(self.phd.lit_review) >= self.num_papers_lit_review:
                # generate formal review
                lit_review_sum = self.phd.format_review()

                # Save the literature review to a markdown file for review
                # Save the literature review to a markdown file for review, only if it's not empty
                if lit_review_sum and lit_review_sum.strip():
                    review_filepath = os.path.join("./research_dir", "literature_review_summary.md")
                    if save_text_to_markdown(lit_review_sum, review_filepath):
                        if self.verbose:
                            print(f"✅ Literature review summary saved to: {review_filepath}")
                    # The save_text_to_markdown function will print its own error on failure.
                else:
                    if self.verbose:
                        print("⚠️  Literature review summary was empty. Skipping save.")
                # if human in loop -> check if human is happy with the produced review
                if self.human_in_loop_flag["literature review"]:
                    retry = self.human_in_loop("literature review", lit_review_sum)
                    # if not happy, repeat the process with human feedback
                    if retry:
                        self.phd.lit_review = []
                        return retry
                # otherwise, return lit review and move on to next stage
                if self.verbose: print(self.phd.lit_review_sum)
                # set agent
                self.set_agent_attr("lit_review_sum", lit_review_sum)
                # reset agent state
                self.reset_agents()
                self.statistics_per_phase["literature review"]["steps"] = _i
                return False
            resp = self.phd.inference(self.research_topic, "literature review", feedback=feedback, step=_i + 1, temp=0.8)
            if self.verbose: print(resp, "\n~~~~~~~~~~~")
        raise Exception("Max tries during phase: Literature Review")

    def human_in_loop(self, phase, phase_prod):
        """
        Get human feedback for phase output
        @param phase: (str) current phase
        @param phase_prod: (str) current phase result
        @return: (bool) whether to repeat the loop
        """
        print("\n\n\n\n\n")
        print(f"Presented is the result of the phase [{phase}]: {phase_prod}")
        y_or_no = None
        # repeat until a valid answer is provided
        while y_or_no not in ["y", "n"]:
            y_or_no = input("\n\n\nAre you happy with the presented content? Respond Y or N: ").strip().lower()
            # if person is happy with feedback, move on to next stage
            if y_or_no == "y": pass
            # if not ask for feedback and repeat
            elif y_or_no == "n":
                # ask the human for feedback
                notes_for_agent = input("Please provide notes for the agent so that they can try again and improve performance: ")
                # reset agent state
                self.reset_agents()
                # add suggestions to the notes
                self.notes.append({
                    "phases": [phase],
                    "note": notes_for_agent})
                return True
            else: print("Invalid response, type Y or N")
        return False



def parse_arguments():
    parser = argparse.ArgumentParser(description="AgentLaboratory Research Workflow")


    parser.add_argument(
        '--copilot-mode',
        type=str,
        default="True",
        help='Enable human interaction mode.'
    )

    parser.add_argument(
        '--load-existing',
        type=str,
        default="False",
        help='Do not load existing state; start a new workflow.'
    )

    parser.add_argument(
        '--load-existing-path',
        type=str,
        help='Path to load existing state; start a new workflow, e.g. state_saves/results_interpretation.pkl'
    )

    parser.add_argument(
        '--research-topic',
        type=str,
        help='Specify the research topic.'
    )

    parser.add_argument(
        '--api-key',
        type=str,
        help='Provide the OpenAI API key.'
    )

    parser.add_argument(
        '--openai-base-url',
        type=str,
        help='Provide the base URL for OpenAI compatible API (e.g., for local LLMs).'
    )

    parser.add_argument(
        '--openai-model-name',
        type=str,
        default="gpt-4o",
        help='Specify the model name to use with OpenAI compatible API.'
    )

    parser.add_argument(
        '--compile-latex',
        type=str,
        default="True",
        help='Compile latex into pdf during paper writing phase. Disable if you can not install pdflatex.'
    )

    parser.add_argument(
        '--llm-backend',
        type=str,
        default="gemini-2.5-flash",
        help='Backend LLM to use for agents in Agent Laboratory.'
    )

    parser.add_argument(
        '--language',
        type=str,
        default="English",
        help='Language to operate Agent Laboratory in.'
    )

    parser.add_argument(
        '--num-papers-lit-review',
        type=str,
        default="5",
        help='Total number of papers to summarize in literature review stage'
    )

    parser.add_argument(
        '--mlesolver-max-steps',
        type=str,
        default="2",
        help='Total number of mle-solver steps'
    )

    parser.add_argument(
        '--papersolver-max-steps',
        type=str,
        default="2",
        help='Total number of paper-solver steps'
    )

    parser.add_argument(
        '--perplexity-api-key',
        type=str,
        help='Perplexity API key for enhanced literature review'
    )

    parser.add_argument(
        '--use-perplexity',
        type=str,
        default="True",
        help='Enable Perplexity for literature review (True/False)'
    )

    parser.add_argument(
        '--literature-engine',
        type=str,
        default="both",
        choices=["arxiv", "perplexity", "both"],
        help='Choose literature search engine: arxiv, perplexity, or both'
    )

    parser.add_argument(
        '--save-docx',
        action="store_true",
        help='Save the final report as a Markdown file instead of compiling PDF if LaTeX is not available.'
    )

    parser.add_argument(
        '--author-name',
        type=str,
        default="Xiao",
        help='Specify the author name for the report.'
    )

    return parser.parse_args()


if __name__ == "__main__":
    args = parse_arguments()

    llm_backend = args.llm_backend
    human_mode = args.copilot_mode.lower() == "true"
    compile_pdf = args.compile_latex.lower() == "true"
    load_existing = args.load_existing.lower() == "true"
    try:
        num_papers_lit_review = int(args.num_papers_lit_review.lower())
    except Exception:
        raise Exception("args.num_papers_lit_review must be a valid integer!")
    try:
        papersolver_max_steps = int(args.papersolver_max_steps.lower())
    except Exception:
        raise Exception("args.papersolver_max_steps must be a valid integer!")
    try:
        mlesolver_max_steps = int(args.mlesolver_max_steps.lower())
    except Exception:
        raise Exception("args.papersolver_max_steps must be a valid integer!")


    api_key = os.getenv('OPENAI_API_KEY') or args.api_key or "your-default-api-key"
    if not api_key:
        raise ValueError("API key must be provided via --api-key or the OPENAI_API_KEY environment variable.")

    # Handle OpenAI compatible model parameters
    openai_base_url = args.openai_base_url
    openai_model_name = args.openai_model_name

    # Handle Perplexity API key
    perplexity_api_key = args.perplexity_api_key or os.getenv('PERPLEXITY_API_KEY')
    if perplexity_api_key:
        os.environ['PERPLEXITY_API_KEY'] = perplexity_api_key
        print("Perplexity API key configured for enhanced literature review.")
    else:
        print("No Perplexity API key provided. Literature review will use ArXiv only.")

    use_perplexity = args.use_perplexity.lower() == "true"
    literature_engine = args.literature_engine
    author_name = args.author_name # Get author_name from args

    ##########################################################
    # Research question that the agents are going to explore #
    ##########################################################
    if human_mode or args.research_topic is None:
        research_topic = input("Please name an experiment idea for AgentLaboratory to perform: ")
    else:
        research_topic = args.research_topic

    task_notes_LLM = [
        {
            "phases": ["literature review"],
            "note": "For the literature review on the subject given, focus on papers that discussed the application of machine learning, shap and pdp explanation."
        },

        {"phases": ["plan formulation"],
         "note": f"You should come up with a plan for TWO experiments."},

        {"phases": ["data preparation"],
         "note": """
        数据准备阶段将由 `research_dir/src/run_analysis.py` 脚本内部处理。
        此阶段不需要生成额外的数据准备代码。
        请确认数据文件 `data/Si2025_5_4.csv` 可用。
        """},




        {"phases": ["results interpretation"],
         "note": """
         在解释模型结果时，请务必参考数据处理阶段提供的详细特征定义。
         特别是要明确指出以下参数的真实含义，避免与原始摘要中的潜在偏差：
         - 'SiO2': 粉煤灰中SiO2的含量 (wt%)。
         - 'Al2O3': 粉煤灰中Al2O3的含量 (wt%)。
         - 'Phase': 粉煤灰中SiO2的晶型，分别为无定形和石英。
         - 'Additives': 粘结剂类型。
         - 'Additive_Ratio': 粘结剂与粉煤灰的质量比。
         - 'water': 指在成型的过程中是否添加水。
         - 'Na2CO3': 活化剂Na2CO3与粉煤灰中SiO2的摩尔比。
         - 'Ca(OH)2': Ca(OH)2与粉煤灰中SiO2的摩尔比。
         - 'Temp': 煅烧温度 (摄氏度)。
         - 'Time': 煅烧反应时间 (min)。
         - 'granular': 成型方式，对应的powder指的是粉煤灰与Na2CO3和CaOH)2直接经过配料混合后的粉末,high_speed指的是使用高速造粒机进行造粒，balling指的是使用低速的滚球造粒机造粒，extruder指的是使用挤条机挤出造粒。
         - 'Na2CO3_CaOH2_Ratio': 活化剂Na2CO3与Ca(OH)2的摩尔比，计算为Na2CO3摩尔比 / (Ca(OH)2摩尔比 + 1e-6)。
         - 'Effective_Silicon': 有效硅含量 (wt%)，即目标变量。
         请确保您的解释与这些定义保持一致，并深入分析它们对有效硅含量的影响。
         """},

        {"phases": ["report writing"],
         "note": f"""
         请在markdown文件上直接替换修改，请将相应生成的图片插入到文档中，请确保最终生成的论文报告详细、深入，目标长度在4000字左右。
         在撰写报告时，**必须严格依据数据处理阶段提供的特征定义来解释和使用所有参数**。
         请特别注意以下参数的准确性：
         - 'SiO2': 粉煤灰中SiO2的含量 (wt%)。
         - 'Al2O3': 粉煤灰中Al2O3的含量 (wt%)。
         - 'Phase': 粉煤灰中SiO2的晶型，分别为无定形和石英。
         - 'Additives': 粘结剂类型。
         - 'Additive_Ratio': 粘结剂与粉煤灰的质量比。
         - 'water': 指在成型的过程中是否添加水。
         - 'Na2CO3': 活化剂Na2CO3与粉煤灰中SiO2的摩尔比。
         - 'Ca(OH)2': Ca(OH)2与粉煤灰中SiO2的摩尔比。
         - 'Temp': 煅烧温度 (摄氏度)。
         - 'Time': 煅烧反应时间 (min)。
         - 'granular': 成型方式，对应的powder指的是粉煤灰与Na2CO3和CaOH)2直接经过配料混合后的粉末,high_speed指的是使用高速造粒机进行造粒，balling指的是使用低速的滚球造粒机造粒，extruder指的是使用挤条机挤出造粒。
         - 'Na2CO3_CaOH2_Ratio': 活化剂Na2CO3与Ca(OH)2的摩尔比，计算为Na2CO3摩尔比 / (Ca(OH)2摩尔比 + 1e-6)。
         - 'Effective_Silicon': 有效硅含量 (wt%)。
         请确保报告中对这些参数的任何提及或分析都与这些准确定义保持一致。
         在摘要和结果部分首次提及某个工艺参数时，请在通用名称后括号中注明其在数据表头中的对应字段名（例如：研磨时间（`granular`）、浸取溶液浓度（`Na2CO3`/`Ca(OH)2`）、煅烧温度（`Temp`）、反应时间（`Time`）等）。
         对于 `Additives` 字段，请始终使用“粘结剂类型”或“粘结剂”。
         对于 `granular` 字段，请在报告中明确解释其代表“粉煤灰的颗粒度或研磨程度”或“机械活化效果”，并在摘要和结果部分使用“研磨时间”时，注明其与 `granular` 的对应关系。

         **实验过程概述：**
         1.  **数据收集与预处理**：数据来源于内部实验室实验，包含了粉煤灰机械化学处理工艺参数和有效硅含量。输入特征包括：SiO2含量、Al2O3含量、Phase、Additives、Additive_Ratio、water、Na2CO3、Ca(OH)2、Temp、Time、granular。目标变量为Effective_Silicon。预处理包括初步探索性数据分析（EDA）、特征工程（如摩尔比和温度-时间交互项），以及数据集划分为训练集和测试集。
         2.  **机器学习模型构建与评估**：系统评估了K近邻（KNN）、随机森林（RF）、XGBoost、LightGBM、CatBoost、多层感知机（MLP）以及Stacking集成模型。采用嵌套交叉验证进行超参数调优和性能评估，评估指标为决定系数（R²）、平均绝对误差（MAE）和均方根误差（RMSE）。
         3.  **可解释性分析方法**：主要采用SHAP（SHapley Additive exPlanations）和PDP（Partial Dependence Plots）进行模型解释。同时，考虑了特征依赖性，探索了近似条件SHAP（通过shap.maskers.Impute）和条件偏依赖图（通过数据子集化）来提供更忠实的解释。

         **硅肥制作过程（粉煤灰处理工艺）概述：**
         本研究的核心在于通过优化粉煤灰的处理工艺来最大化有效硅的提取。这是一个多步骤的物理化学转化过程：
         - **原料混合**：以粉煤灰为原料，将其与两种活化剂“Na2CO3”和“Ca(OH)2”按照与粉煤灰中SiO2的含量的一定摩尔比进行混合。
         - **粘结与成型**：根据实验设计，通过添加不同类型（`Additives`）的粘结剂进行混合。在此过程中，可能添加适当的水（`water` 指在成型的过程中是否添加水）以制成膏状，然后通过不同的成型方式（`granular` 字段：`powder` 指粉煤灰与活化剂直接混合后的粉末；`high_speed` 指使用高速造粒机进行造粒；`balling` 指使用低速滚球造粒机造粒；`extruder` 指使用挤条机挤出造粒）进行成型。如果选择 `powder` 成型方式，则此过程不添加水。
         - **热活化（煅烧）**：对成型后的混合物进行活化处理。通过精确控制“Temp”（煅烧温度，摄氏度）和“Time”（煅烧反应时间，min）来控制反应条件，进一步改变粉煤灰的矿物相组成和结构，促进硅相的非晶化，使其更易于浸取。
         - **有效硅测试**：对得到的煅烧产物进行可溶性硅含量的测试，从而获得“Effective_Silicon”（有效硅含量）。
         整个制作过程是一个复杂的系统，各工艺参数之间存在显著的非线性相互作用和协同效应，共同影响最终的有效硅提取效率。

         **机器学习分析过程概述：**
         本研究利用机器学习模型来理解和优化上述复杂的硅肥制作过程：
         - **高精度预测**：通过训练多种机器学习模型（如CatBoost、Stacking等），建立从工艺参数到有效硅含量的预测模型，实现高精度的预测，为工艺优化提供数据驱动的依据。
         - **关键参数识别**：利用SHAP等可解释性工具，量化识别出对有效硅含量影响最大的关键工艺参数。
         - **非线性关系和交互作用揭示**：通过PDP和SHAP依赖图等工具，深入揭示单个参数的非线性边际效应，以及不同参数之间（如研磨时间与煅烧温度）的复杂协同增强效应，从而提供深层的机理洞察。
         - **指导工艺优化**：模型的预测和解释结果直接指导工业生产中硅提取工艺的优化策略，例如确定最佳的研磨时间、煅烧温度区间以及活化剂配比等。
         """},

        {"phases": ["report refinement"],
        "note": f"请在markdown文件上直接替换修改"},

    ]

    task_notes_LLM.append(
        {"phases": ["literature review", "plan formulation", "data preparation", "running experiments", "results interpretation", "report writing", "report refinement"],
        "note": f"You should always write in the following language to converse and to write the report {args.language}"},
    )

    ####################################################
    ###  Stages where human input will be requested  ###
    ####################################################
    human_in_loop = {
        "literature review":      human_mode,
        "plan formulation":       human_mode,
        "data preparation":       human_mode,
        "running experiments":    human_mode,
        "results interpretation": human_mode,
        "report writing":         human_mode,
        "report refinement":      human_mode,
    }

    ###################################################
    ###  LLM Backend used for the different phases  ###
    ###################################################
    agent_models = {
        "literature review":      llm_backend,
        "plan formulation":       llm_backend,
        "data preparation":       llm_backend,
        "running experiments":    llm_backend,
        "report writing":         llm_backend,
        "results interpretation": llm_backend,
        "paper refinement":       llm_backend,
    }

    if llm_backend not in [ "gemini-1.5-flash", "gemini-2.5-flash", "gemini-2.5-flash-preview-04-17", "gemini-flash", "gemini-flash-2.5", "gemini-2.5-pro"]:
        raise ValueError(f"Invalid llm_backend: {llm_backend}")

    if load_existing:
        load_path = args.load_existing_path
        if load_path is None:
            raise ValueError("Please provide path to load existing state.")
        if "save_states" in load_path:
            load_path = load_path.replace("save_states", "state_saves")
        with open(load_path, "rb") as f:
            lab = pickle.load(f)
            # Force update API configurations from command line arguments after loading state
            if args.api_key:
                lab.openai_api_key = api_key
                lab.set_agent_attr("openai_api_key", api_key)
                print(f"--- [INFO] API key updated to: {'****' + api_key[-4:]}")
            if args.openai_base_url:
                lab.openai_base_url = openai_base_url
                lab.set_agent_attr("openai_base_url", openai_base_url)
                print(f"--- [INFO] OpenAI base URL updated to: {openai_base_url}")
            if args.openai_model_name:
                lab.openai_model_name = openai_model_name
                lab.set_agent_attr("openai_model_name", openai_model_name)
                print(f"--- [INFO] OpenAI model name updated to: {openai_model_name}")
            
            # Ensure author_name is set for loaded instances
            if hasattr(args, 'author_name') and args.author_name:
                lab.author_name = args.author_name
                print(f"--- [INFO] Author name for loaded instance updated to: {args.author_name}")
            elif not hasattr(lab, 'author_name'):
                # If the loaded object doesn't have author_name and no command line arg was provided for it
                # set to default. The args.author_name from parse_arguments() already has a default.
                lab.author_name = args.author_name # Use the default from args or the one provided
                print(f"--- [INFO] Author name for loaded instance set to: {lab.author_name}")
            
            # Force update human_in_loop_flag based on command line arguments after loading state
            lab.human_in_loop_flag = human_in_loop # human_in_loop is derived from args.copilot_mode
            if lab.verbose: print(f"--- [INFO] human_in_loop_flag updated to: {lab.human_in_loop_flag}")

            # Reset phases from the loaded state based on --load-existing-path
            # Extract the phase name from the load_existing_path
            loaded_phase_name = os.path.basename(load_path).replace(".pkl", "")
            
            # Find the index of the loaded phase in the phases list
            phase_found = False
            for phase_group_idx, (phase, subtasks) in enumerate(lab.phases):
                for subtask in subtasks:
                    if subtask.replace(" ", "_") == loaded_phase_name:
                        phase_found = True
                        break
                if phase_found:
                    break
            
            if phase_found:
                # Set the loaded phase and all subsequent phases to False
                reset_from_this_point = False
                found_loaded_phase = False
                for phase, subtasks in lab.phases:
                    for subtask in subtasks:
                        if subtask.replace(" ", "_") == loaded_phase_name:
                            found_loaded_phase = True
                            # Don't reset the loaded phase itself, start from the next one
                            continue
                        if found_loaded_phase:
                            reset_from_this_point = True
                        if reset_from_this_point:
                            lab.phase_status[subtask] = False
                            if lab.verbose: print(f"--- [INFO] Resetting phase status for '{subtask}' to False to restart from after '{loaded_phase_name}'")
            else:
                if lab.verbose: print(f"--- [WARNING] Loaded phase '{loaded_phase_name}' not found in defined phases. No phase status reset performed.")
            
            print(f"--- [DEBUG] Phase status after loading and resetting: {lab.phase_status}") # Added debug print
            
            # Set the starting subtask for perform_research to the next phase after loaded_phase_name
            next_subtask = None
            found_loaded_phase = False
            for phase, subtasks in lab.phases:
                for subtask in subtasks:
                    if found_loaded_phase and next_subtask is None:
                        next_subtask = subtask.replace(" ", "_")
                        break
                    if subtask.replace(" ", "_") == loaded_phase_name:
                        found_loaded_phase = True
                if next_subtask:
                    break

            lab.start_subtask = next_subtask
            if lab.verbose: print(f"--- [INFO] Setting start_subtask for resuming to: {lab.start_subtask} (next phase after {loaded_phase_name})")


    else:
        # remove previous files only if not loading existing state
        remove_figures()
        remove_directory("research_dir")
        # make src and research directory
        if not os.path.exists("outputs/state_saves"):
            os.makedirs("outputs/state_saves", exist_ok=True)
        if not os.path.exists("research_dir"):
            os.mkdir("research_dir")
        if not os.path.exists("research_dir/src"):
            os.mkdir("research_dir/src")
        if not os.path.exists("research_dir/tex"):
            os.mkdir("research_dir/tex")

        lab = LaboratoryWorkflow(
            research_topic=research_topic,
            notes=task_notes_LLM,
            agent_model_backbone=agent_models,
            human_in_loop_flag=human_in_loop,
            openai_api_key=api_key,
            openai_base_url=openai_base_url,
            openai_model_name=openai_model_name,
            compile_pdf=compile_pdf,
            num_papers_lit_review=num_papers_lit_review,
            papersolver_max_steps=papersolver_max_steps,
            mlesolver_max_steps=mlesolver_max_steps,
            args=args,
            author_name=author_name, # Pass author_name
        )
    
    # Add debug print for initial phase status in perform_research
    if lab.verbose: print(f"--- [DEBUG] Initial phase status before perform_research: {lab.phase_status}") # Added debug print
    lab.perform_research()
