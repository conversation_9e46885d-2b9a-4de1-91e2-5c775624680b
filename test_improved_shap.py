#!/usr/bin/env python3
"""
测试改进的 SHAP 和 PDP 可视化功能
"""

import os
import sys
import datetime
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import OneHotEncoder, RobustScaler
from sklearn.compose import ColumnTransformer

# 添加 src 目录到路径
sys.path.append('src')

def test_improved_shap():
    """测试改进的 SHAP 功能"""
    print("开始测试改进的 SHAP 和 PDP 功能...")
    
    # 创建输出目录
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_folder = f"test_improved_shap_{timestamp}"
    os.makedirs(output_folder, exist_ok=True)
    print(f"输出目录: {output_folder}")
    
    try:
        # 1. 加载和预处理数据
        print("1. 加载数据...")
        df = pd.read_csv("data/Si2025_5_4.csv")
        target_column = "Effective_Silicon"
        
        # 简单特征工程
        df['Na2CO3_CaOH2_Ratio'] = df['Na2CO3'] / (df['Ca(OH)2'] + 1e-8)
        df['Temp_Time_Interaction'] = df['Temp'] * df['Time']
        
        X = df.drop(columns=[target_column])
        y = df[target_column]
        
        # 识别特征类型
        numeric_features = X.select_dtypes(include=[np.number]).columns.tolist()
        categorical_features = X.select_dtypes(include=['object']).columns.tolist()
        
        print(f"数值特征: {numeric_features}")
        print(f"分类特征: {categorical_features}")
        
        # 创建预处理器
        preprocessor = ColumnTransformer(
            transformers=[
                ('num', RobustScaler(), numeric_features),
                ('cat', OneHotEncoder(handle_unknown='ignore', sparse_output=False), categorical_features)
            ]
        )
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 预处理
        X_train_processed = preprocessor.fit_transform(X_train)
        X_test_processed = preprocessor.transform(X_test)
        
        # 获取特征名称
        try:
            feature_names_list = preprocessor.get_feature_names_out()
        except AttributeError:
            # 备用方案
            num_features = list(X_train.select_dtypes(include=np.number).columns)
            cat_transformer = preprocessor.named_transformers_.get('cat')
            if cat_transformer and hasattr(cat_transformer, 'get_feature_names_out'):
                ohe_features = list(cat_transformer.get_feature_names_out())
            else:
                ohe_features = []
            feature_names_list = num_features + ohe_features
        
        # 转换为 DataFrame
        X_train_df = pd.DataFrame(X_train_processed, columns=feature_names_list)
        X_test_df = pd.DataFrame(X_test_processed, columns=feature_names_list)
        
        print(f"处理后特征数量: {len(feature_names_list)}")
        
        # 2. 训练模型
        print("2. 训练随机森林模型...")
        model = RandomForestRegressor(n_estimators=100, random_state=42)
        model.fit(X_train_processed, y_train)
        
        test_score = model.score(X_test_processed, y_test)
        print(f"测试集 R²: {test_score:.4f}")
        
        # 3. 测试改进的 SHAP 分析
        print("3. 测试改进的 SHAP 分析...")
        
        from analysis_utils.improved_interpretation import robust_shap_analysis, robust_pdp_analysis
        
        # SHAP 分析
        shap_plot_paths = robust_shap_analysis(
            model, 
            X_train_df, 
            X_test_df, 
            feature_names_list, 
            output_folder, 
            "RandomForest_Test"
        )
        
        print(f"生成了 {len(shap_plot_paths)} 个 SHAP 图:")
        for path in shap_plot_paths:
            print(f"  - {path}")
        
        # 4. 测试改进的 PDP 分析
        print("4. 测试改进的 PDP 分析...")
        
        pdp_path = robust_pdp_analysis(
            model, 
            X_test_df, 
            feature_names_list, 
            output_folder, 
            "RandomForest_Test"
        )
        
        if pdp_path:
            print(f"PDP 分析保存到: {pdp_path}")
        else:
            print("PDP 分析失败")
        
        # 5. 检查生成的文件
        print("\n5. 检查生成的文件...")
        generated_files = [f for f in os.listdir(output_folder) if f.endswith('.png')]
        print(f"生成的图片文件: {generated_files}")
        
        # 验证文件存在
        all_files_exist = True
        for path in shap_plot_paths:
            if not os.path.exists(path):
                print(f"❌ 文件不存在: {path}")
                all_files_exist = False
            else:
                print(f"✅ 文件存在: {os.path.basename(path)}")
        
        if pdp_path and os.path.exists(pdp_path):
            print(f"✅ PDP 文件存在: {os.path.basename(pdp_path)}")
        elif pdp_path:
            print(f"❌ PDP 文件不存在: {pdp_path}")
            all_files_exist = False
        
        if all_files_exist:
            print("\n🎉 所有改进的 SHAP 和 PDP 功能测试成功！")
            return True
        else:
            print("\n⚠️ 部分文件生成失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_improved_shap()
    if success:
        print("\n✅ 改进的 SHAP 和 PDP 功能正常工作！")
    else:
        print("\n❌ 仍有问题需要解决")
