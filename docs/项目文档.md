# AgentLaboratory 项目文档

## 📋 目录
1. [项目概述](#项目概述)
2. [系统架构](#系统架构)
3. [核心模块详解](#核心模块详解)
4. [工作流程](#工作流程)
5. [安装与配置](#安装与配置)
6. [使用指南](#使用指南)
7. [常见问题](#常见问题)
8. [故障排除](#故障排除)

## 🎯 项目概述

### 什么是 AgentLaboratory？

AgentLaboratory 是一个端到端的自主研究工作流系统，旨在协助人类研究者实现研究想法。该系统由大语言模型驱动的专业化智能体组成，支持完整的研究工作流程——从文献综述和计划制定到实验执行和撰写综合报告。

### 核心特性

- **🤖 多智能体协作**：由多个专业化AI智能体组成，包括博士生、博士后、教授、机器学习工程师等角色
- **📚 自动文献综述**：集成arXiv API，自动搜索和分析相关研究论文
- **🔬 实验自动化**：自动生成和执行Python代码进行数据分析和机器学习实验
- **📊 智能数据处理**：支持多种数据格式，自动进行特征工程和数据预处理
- **📝 报告生成**：自动生成Word格式的研究报告，包含完整的实验结果和分析
- **🔄 迭代优化**：支持多轮实验和结果改进

### 设计理念

该系统不是为了替代研究者的创造力，而是为了补充它，使研究者能够专注于创意和批判性思维，同时自动化重复性和耗时的任务，如编码和文档编写。

## 🏗️ 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                    AgentLaboratory 系统架构                      │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   文献综述阶段   │  │   实验执行阶段   │  │   报告撰写阶段   │  │
│  │                │  │                │  │                │  │
│  │ • PhD Student  │  │ • PhD Student  │  │ • Professor    │  │
│  │ • arXiv API    │  │ • ML Engineer  │  │ • Reviewers    │  │
│  │ • 论文搜索     │  │ • 数据处理     │  │ • 报告生成     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                         核心组件                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  LLM 推理引擎   │  │   工具集成      │  │   状态管理      │  │
│  │                │  │                │  │                │  │
│  │ • Gemini API   │  │ • HuggingFace  │  │ • 阶段跟踪     │  │
│  │ • 重试机制     │  │ • Python执行   │  │ • 成本计算     │  │
│  │ • 错误处理     │  │ • 文件管理     │  │ • 结果保存     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### 三大核心阶段

1. **文献综述阶段 (Literature Review)**
   - 自动搜索相关研究论文
   - 分析和总结现有研究
   - 识别研究空白和机会

2. **实验执行阶段 (Experimentation)**
   - 计划制定 (Plan Formulation)
   - 数据准备 (Data Preparation)
   - 实验运行 (Running Experiments)

3. **报告撰写阶段 (Report Writing)**
   - 结果解释 (Results Interpretation)
   - 报告撰写 (Report Writing)
   - 报告完善 (Report Refinement)

## 🧩 核心模块详解

### 1. 主控制器 (`ai_lab_repo.py`)

**LaboratoryWorkflow 类**
- **功能**：整个研究流程的核心控制器
- **职责**：
  - 管理研究流程的各个阶段
  - 协调不同智能体之间的交互
  - 跟踪实验状态和成本
  - 处理用户配置和参数

**关键方法**：
```python
def perform_research(self):
    """执行完整的研究流程"""
    
def literature_review(self):
    """文献综述阶段"""
    
def plan_formulation(self):
    """计划制定阶段"""
    
def data_preparation(self):
    """数据准备阶段"""
    
def running_experiments(self):
    """实验运行阶段"""
```

### 2. 智能体系统 (`agents.py`)

#### 基础智能体类 (BaseAgent)
- **功能**：所有智能体的基类
- **职责**：
  - 提供通用的推理接口
  - 管理对话历史
  - 处理成本计算

#### 专业化智能体

**PhDStudentAgent (博士生智能体)**
- **角色**：研究执行者
- **职责**：
  - 执行文献综述
  - 制定实验计划
  - 进行数据分析
  - 撰写研究报告
- **支持阶段**：所有研究阶段

**PostdocAgent (博士后智能体)**
- **角色**：研究指导者
- **职责**：
  - 指导实验计划制定
  - 提供研究建议
  - 协助结果解释
- **支持阶段**：计划制定、结果解释

**MLEngineerAgent (机器学习工程师智能体)**
- **角色**：技术实现者
- **职责**：
  - 数据预处理和特征工程
  - 模型构建和训练
  - 实验代码编写
- **支持阶段**：数据准备、实验运行

**ProfessorAgent (教授智能体)**
- **角色**：学术指导者
- **职责**：
  - 提供高层次的研究指导
  - 审查研究质量
  - 指导报告撰写
- **支持阶段**：报告撰写

**ReviewersAgent (审稿人智能体)**
- **角色**：质量控制者
- **职责**：
  - 审查研究结果
  - 提供改进建议
  - 确保研究质量
- **支持阶段**：报告完善

### 3. 推理引擎 (`inference.py`)

**核心功能**：
- **LLM API 集成**：支持多种大语言模型
- **重试机制**：处理API调用失败
- **错误处理**：优雅处理各种异常情况
- **成本跟踪**：监控API使用成本

**关键函数**：
```python
def query_model(model_str, system_prompt, prompt, temp=0.7):
    """查询大语言模型"""
    
def gemini_inference(system_prompt, prompt, temp=0.7):
    """Gemini模型推理"""
```

### 4. 工具集 (`tools.py`)

**文件管理工具**：
- `remove_directory()`: 删除目录
- `remove_figures()`: 清理图片文件
- `save_to_file()`: 保存文件

**数据处理工具**：
- `extract_code_from_response()`: 从响应中提取代码
- `execute_python_code()`: 执行Python代码
- `validate_code_syntax()`: 验证代码语法

### 5. 实用工具 (`utils.py`)

**配置管理**：
- 环境变量处理
- 参数验证
- 默认值设置

**辅助功能**：
- 日志记录
- 状态序列化
- 错误报告

## 🔄 工作流程

### 完整研究流程

```mermaid
graph TD
    A[开始研究] --> B[文献综述]
    B --> C{需要更多论文?}
    C -->|是| B
    C -->|否| D[计划制定]
    D --> E[数据准备]
    E --> F[实验运行]
    F --> G[结果解释]
    G --> H{需要改进?}
    H -->|是| E
    H -->|否| I[报告撰写]
    I --> J[报告完善]
    J --> K{质量满意?}
    K -->|否| I
    K -->|是| L[完成研究]
```

### 详细阶段说明

#### 1. 文献综述阶段
```
输入：研究主题
过程：
  1. PhD Student 搜索 arXiv 论文
  2. 分析论文摘要和内容
  3. 总结相关研究
  4. 识别研究空白
输出：文献综述报告
```

#### 2. 计划制定阶段
```
输入：研究主题 + 文献综述
过程：
  1. Postdoc 提供高层次指导
  2. PhD Student 制定详细实验计划
  3. 确定实验方法和评估指标
  4. 规划数据需求和处理流程
输出：实验计划
```

#### 3. 数据准备阶段
```
输入：实验计划 + 数据源
过程：
  1. PhD Student 指导数据处理
  2. ML Engineer 编写数据处理代码
  3. 执行数据清洗和特征工程
  4. 验证数据质量
输出：处理后的数据集
```

#### 4. 实验运行阶段
```
输入：处理后的数据 + 实验计划
过程：
  1. ML Engineer 构建和训练模型
  2. 执行实验和评估
  3. 记录实验结果
  4. 生成可视化图表
输出：实验结果和模型
```

#### 5. 结果解释阶段
```
输入：实验结果
过程：
  1. Postdoc 指导结果分析
  2. PhD Student 解释实验发现
  3. 分析模型性能和局限性
  4. 提出改进建议
输出：结果分析报告
```

#### 6. 报告撰写阶段
```
输入：所有前期成果
过程：
  1. Professor 指导报告结构
  2. PhD Student 撰写报告内容
  3. Reviewers 审查和改进
  4. 生成最终报告
输出：完整研究报告 (.docx格式)
```

## ⚙️ 安装与配置

### 系统要求

- **Python**: 3.8+
- **操作系统**: macOS, Linux, Windows
- **内存**: 建议8GB以上
- **网络**: 稳定的互联网连接（用于API调用）

### 安装步骤

1. **克隆仓库**
```bash
git clone https://github.com/SamuelSchmidgall/AgentLaboratory.git
cd AgentLaboratory
```

2. **创建虚拟环境**
```bash
python -m venv venv_agent_lab
source venv_agent_lab/bin/activate  # Linux/macOS
# 或
venv_agent_lab\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

### 环境配置

#### 1. Google API 密钥配置

**获取API密钥**：
1. 访问 [Google AI Studio](https://aistudio.google.com/app/apikey)
2. 创建新的API密钥

**设置环境变量**：
```bash
# 方法1：临时设置
export GOOGLE_API_KEY="your_api_key_here"

# 方法2：永久设置
echo 'export GOOGLE_API_KEY="your_api_key_here"' >> ~/.bashrc
source ~/.bashrc

# 方法3：使用.env文件
echo 'GOOGLE_API_KEY=your_api_key_here' > .env
```

#### 2. SSL/TLS 错误解决

如果遇到SSL/TLS连接错误，设置以下环境变量：
```bash
export GRPC_SSL_CIPHER_SUITES="HIGH"
export GRPC_EXPERIMENTAL_DISABLE_GRPCLB=1
```

### 依赖包说明

**核心依赖**：
- `google-generativeai`: Google Gemini API客户端
- `pandas`: 数据处理
- `numpy`: 数值计算
- `scikit-learn`: 机器学习
- `matplotlib`: 数据可视化
- `seaborn`: 统计可视化
- `python-docx`: Word文档生成
- `requests`: HTTP请求
- `tiktoken`: 令牌计数

**可选依赖**：
- `shap`: 模型解释
- `transformers`: HuggingFace模型
- `torch`: 深度学习框架

## 📖 使用指南

### 基本使用

#### 1. 快速开始

**最简单的使用方式**：
```bash
python ai_lab_repo.py \
  --research-topic "你的研究主题" \
  --llm-backend "gemini-2.5-flash-preview-05-20" \
  --copilot-mode "False" \
  --language "中文" \
  --num-papers-lit-review "5" \
  --save-docx
```

#### 2. 参数详解

**必需参数**：
- `--research-topic`: 研究主题描述
- `--llm-backend`: 使用的语言模型

**可选参数**：
- `--copilot-mode`: 是否启用交互模式 ("True"/"False")
- `--language`: 使用语言 ("中文"/"English")
- `--num-papers-lit-review`: 文献综述论文数量 (0-20)
- `--save-docx`: 保存Word格式报告
- `--verbose`: 详细输出模式

#### 3. 支持的模型

**Gemini 系列**：
- `gemini-2.5-flash-preview-05-20` (推荐)
- `gemini-flash-2.5`
- `gemini-2.5-flash-preview-04-17`

### 高级使用

#### 1. 自定义数据分析

**使用本地数据集**：
```bash
python ai_lab_repo.py \
  --research-topic "基于 /path/to/your/data.csv 数据集，进行机器学习分析，预测目标变量 'target_column'。请进行完整的EDA、特征工程、模型构建和评估。" \
  --llm-backend "gemini-2.5-flash-preview-05-20" \
  --copilot-mode "False" \
  --language "中文" \
  --num-papers-lit-review "0" \
  --save-docx
```

#### 2. 交互式模式

**启用人工干预**：
```bash
python ai_lab_repo.py \
  --research-topic "你的研究主题" \
  --llm-backend "gemini-2.5-flash-preview-05-20" \
  --copilot-mode "True" \
  --language "中文"
```

在交互式模式下，系统会在关键阶段询问用户意见和确认。

#### 3. 自定义实验配置

**修改实验参数**：
编辑 `ai_lab_repo.py` 中的 `task_notes_LLM` 结构：

```python
task_notes_LLM = [
    {"phases": ["plan formulation"],
     "note": "请设计两个对比实验：基线模型和改进模型。"},
    {"phases": ["data preparation"],
     "note": "使用标准化预处理，处理缺失值，进行特征选择。"},
    {"phases": ["running experiments"],
     "note": "使用5折交叉验证，记录详细的性能指标。"},
]
```

### 输出文件说明

#### 1. 目录结构

运行完成后，会生成以下目录结构：
```
research_dir/
├── src/                    # 生成的源代码
│   ├── data_preparation.py # 数据预处理代码
│   ├── experiment_1.py     # 实验1代码
│   └── experiment_2.py     # 实验2代码
├── figures/                # 生成的图表
│   ├── eda_plots.png      # EDA图表
│   ├── model_performance.png # 模型性能图
│   └── feature_importance.png # 特征重要性图
└── research_report.docx    # 最终研究报告
```

#### 2. 报告内容

**研究报告包含**：
- 研究背景和目标
- 文献综述总结
- 数据描述和预处理
- 实验设计和方法
- 结果分析和讨论
- 结论和未来工作

### 最佳实践

#### 1. 研究主题描述

**好的研究主题示例**：
```
"基于客户行为数据，构建机器学习模型预测客户流失。数据包含客户基本信息、消费记录、服务使用情况等特征。请进行完整的探索性数据分析，特征工程，构建多个对比模型（逻辑回归、随机森林、XGBoost），并使用SHAP进行模型解释。"
```

**避免的描述**：
- 过于简单："预测客户流失"
- 过于复杂：包含太多技术细节的长篇描述

#### 2. 数据准备建议

- **数据格式**：推荐使用CSV格式
- **数据大小**：建议小于100MB以确保处理效率
- **列名**：使用英文列名，避免特殊字符
- **目标变量**：明确指定目标变量名称

#### 3. 性能优化

- **跳过文献综述**：如果不需要文献综述，设置 `--num-papers-lit-review "0"`
- **使用非交互模式**：设置 `--copilot-mode "False"` 以避免等待用户输入
- **选择合适的模型**：`gemini-2.5-flash-preview-05-20` 通常提供最佳性能

## ❓ 常见问题

### Q1: 如何处理API密钥错误？

**问题**：`API key not valid. Please pass a valid API key.`

**解决方案**：
1. 确认API密钥正确设置
2. 检查环境变量是否生效
3. 验证API密钥权限

```bash
# 检查环境变量
echo $GOOGLE_API_KEY

# 重新设置API密钥
export GOOGLE_API_KEY="your_correct_api_key"
```

### Q2: 如何解决模型不存在错误？

**问题**：`models/gemini-flash-2.5 is not found`

**解决方案**：
使用正确的模型名称：
```bash
--llm-backend "gemini-2.5-flash-preview-05-20"
```

### Q3: 如何处理SSL/TLS连接错误？

**问题**：`SSL routines:OPENSSL_internal:TLSV1_ALERT_PROTOCOL_VERSION`

**解决方案**：
```bash
export GRPC_SSL_CIPHER_SUITES="HIGH"
export GRPC_EXPERIMENTAL_DISABLE_GRPCLB=1
```

### Q4: 程序卡在交互式输入怎么办？

**问题**：程序等待用户输入研究主题

**解决方案**：
1. 使用非交互模式：`--copilot-mode "False"`
2. 或者在终端中输入研究主题

### Q5: 如何处理内存不足错误？

**问题**：处理大数据集时内存不足

**解决方案**：
1. 减小数据集大小
2. 增加系统内存
3. 使用数据采样技术

### Q6: 生成的代码有语法错误怎么办？

**问题**：AI生成的代码无法执行

**解决方案**：
1. 系统会自动重试（最多3次）
2. 检查数据路径是否正确
3. 确认所需的Python包已安装

## 🔧 故障排除

### 调试模式

启用详细输出以获取更多调试信息：
```bash
python ai_lab_repo.py --verbose ...其他参数...
```

### 日志分析

**查看错误日志**：
- 检查终端输出中的错误信息
- 查看 `research_dir/` 目录中的生成文件
- 检查代码执行结果

**常见错误模式**：
1. **API调用失败**：检查网络连接和API密钥
2. **代码执行错误**：检查数据路径和依赖包
3. **内存不足**：减小数据集或增加内存

### 手动干预

如果自动化流程失败，可以：
1. 检查 `research_dir/src/` 中生成的代码
2. 手动修复代码错误
3. 重新运行特定阶段

### 性能监控

**监控API使用**：
- 系统会显示估算的API成本
- 监控API调用频率
- 避免超出API限制

**监控系统资源**：
```bash
# 监控内存使用
top -p $(pgrep -f ai_lab_repo.py)

# 监控磁盘空间
df -h
```

### 恢复机制

**状态保存**：
- 系统会在每个阶段保存状态
- 可以从中断点恢复执行
- 状态文件保存在 `state_saves/` 目录

**手动恢复**：
```bash
# 从保存的状态恢复
python ai_lab_repo.py --load-existing-path "state_saves/your_state.pkl"
```

---

## 📞 技术支持

如果您在使用过程中遇到问题，可以：

1. **查看本文档**：首先查看相关章节
2. **检查GitHub Issues**：查看是否有类似问题
3. **创建新Issue**：详细描述问题和错误信息
4. **联系开发者**：发送邮件至项目维护者

**报告问题时请包含**：
- 完整的错误信息
- 使用的命令和参数
- 系统环境信息
- 数据集描述（如适用）

---

*本文档持续更新中，如有疑问或建议，欢迎反馈。*
