# Perplexity AI 文献综述使用指南

## 概述

Agent Laboratory 现已将 Perplexity AI 设置为**主要的文献综述工具**，提供比传统 ArXiv 搜索更全面、更智能的文献分析能力。

## 🚀 主要优势

### 1. 多数据库覆盖
- **PubMed**: 生物医学文献
- **ArXiv**: 预印本论文
- **Google Scholar**: 学术搜索
- **IEEE Xplore**: 工程技术文献

### 2. AI 驱动分析
- 智能内容理解和分析
- 自动生成引用
- 上下文相关的文献综述
- 研究趋势识别

### 3. 成本优化
- 自动选择最适合的模型
- 预算控制和成本估算
- 智能查询优化

## 📋 使用方法

### 基本命令

在文献综述阶段，PhD 学生代理现在优先使用以下命令：

```bash
# 主要推荐命令 - Perplexity 综合文献综述
```PERPLEXITY_REVIEW
你的研究主题
```

### 完整工作流示例

```bash
# 1. 启动 Agent Laboratory 并设置 Perplexity
export PERPLEXITY_API_KEY="your_perplexity_api_key"

# 2. 运行 Agent Laboratory
python ai_lab_repo.py \
  --research-topic "机器学习在材料科学中的应用" \
  --api-key "your_google_api_key" \
  --literature-engine "both" \
  --language "中文"
```

## 🎯 优化策略

### 1. 研究主题分类自动优化

系统会根据研究主题自动选择最佳配置：

- **机器学习/AI**: 优先搜索 ArXiv + IEEE + Google Scholar
- **材料科学**: 重点关注 PubMed + Nature + ArXiv
- **生物医学**: 主要使用 PubMed + Google Scholar

### 2. 模型选择策略

- **综合文献综述**: Sonar Pro (最佳质量)
- **快速搜索**: Sonar Small (成本效益)
- **深度分析**: Sonar Huge (最强能力)

### 3. 成本控制

- 自动预算管理 (默认 $15 限额)
- 实时成本跟踪
- 智能查询优化

## 📊 使用统计

系统会自动跟踪和显示：

- 总查询次数
- 累计成本
- 预算使用情况
- 文献覆盖范围

## 🔧 高级配置

### 自定义预算限制

```python
# 在 ai_lab_repo.py 中修改
perplexity_config = setup_perplexity_for_agent_lab(
    self.research_topic, 
    budget=25.0  # 设置 $25 预算限制
)
```

### 自定义搜索域

```python
# 在 perplexity_config.py 中添加自定义域
DOMAIN_FILTERS["custom_area"] = [
    "your-domain.com",
    "arxiv.org",
    "scholar.google.com"
]
```

## 🎨 文献综述模板

### 1. 综合模板 (默认)
- 背景介绍
- 研究现状
- 方法论分析
- 最新发展
- 研究空白
- 未来方向

### 2. 聚焦模板
- 核心概念
- 关键发现
- 主要方法
- 当前挑战
- 最新突破

### 3. 方法论模板
- 已建立的方法
- 新兴方法
- 比较研究
- 性能基准
- 实施挑战

## 🚨 故障排除

### 常见问题

1. **API Key 错误**
   ```bash
   export PERPLEXITY_API_KEY="your_actual_key"
   ```

2. **预算超限**
   - 检查当前使用情况
   - 调整预算限制
   - 使用更经济的模型

3. **搜索结果为空**
   - 调整搜索查询
   - 检查研究领域匹配
   - 尝试不同的模板

### 调试模式

```bash
python ai_lab_repo.py --verbose [其他参数...]
```

## 📈 性能对比

| 功能 | ArXiv 搜索 | Perplexity AI |
|------|------------|---------------|
| 数据库覆盖 | 仅 ArXiv | 多数据库 |
| AI 分析 | 无 | 智能分析 |
| 自动引用 | 手动 | 自动生成 |
| 成本控制 | 免费 | 智能优化 |
| 综述质量 | 基础 | 专业级 |

## 🎯 最佳实践

### 1. 查询优化
- 使用具体、明确的研究术语
- 避免过于宽泛的查询
- 结合领域特定关键词

### 2. 成本管理
- 设置合理的预算限制
- 优先使用 Sonar Small 进行初步搜索
- 保留 Sonar Pro 用于最终综述

### 3. 结果验证
- 交叉验证重要发现
- 检查引用的准确性
- 补充特定领域的专业数据库

## 🔮 未来发展

计划中的功能增强：

- [ ] 自动文献质量评估
- [ ] 多语言文献综述支持
- [ ] 实时文献更新通知
- [ ] 协作文献综述功能
- [ ] 可视化文献网络分析

## 📞 支持与反馈

如果遇到问题或有改进建议：

1. 检查现有的 Issues
2. 创建新的 Issue 描述问题
3. 提供详细的错误信息和复现步骤

---

**注意**: Perplexity AI 集成是一个持续改进的功能，我们会根据用户反馈不断优化和完善。
