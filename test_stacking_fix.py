#!/usr/bin/env python3
"""
测试修复后的 Stacking 模型解释功能
"""

import os
import sys
import subprocess

def test_stacking_fix():
    """测试修复后的 Stacking 分析"""
    print("开始测试修复后的 Stacking 模型解释功能...")
    
    try:
        # 运行完整的分析脚本
        print("运行 run_analysis.py...")
        
        cmd = [
            sys.executable, 
            "src/run_analysis.py", 
            "--data_path", "data/Si2025_5_4.csv",
            "--target_column", "Effective_Silicon"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 运行命令并捕获输出
        result = subprocess.run(
            cmd, 
            cwd="/Users/<USER>/Documents/Python/agentlaboratory624",
            capture_output=True, 
            text=True, 
            timeout=600  # 10分钟超时
        )
        
        print(f"返回码: {result.returncode}")
        
        # 检查输出中的关键信息
        output = result.stdout
        
        # 检查 Stacking 分析相关的输出
        stacking_keywords = [
            "STARTING MULTI-LEVEL STACKING MODEL INTERPRETATION",
            "Level 1: Analyzing Base Learners",
            "Level 2: Analyzing the Meta-Learner", 
            "Level 3: Analyzing Entire Stacking Model",
            "Base learners SHAP plots saved to",
            "Generated"
        ]
        
        print("\n=== Stacking 分析检查 ===")
        for keyword in stacking_keywords:
            if keyword in output:
                print(f"✅ 找到: {keyword}")
            else:
                print(f"❌ 未找到: {keyword}")
        
        # 检查错误信息
        error_keywords = [
            "feature_names mismatch",
            "Feature names unseen at fit time",
            "Input X contains NaN",
            "ERROR generating SHAP for base learner"
        ]
        
        print("\n=== 错误检查 ===")
        error_found = False
        for keyword in error_keywords:
            if keyword in output:
                print(f"❌ 仍有错误: {keyword}")
                error_found = True
        
        if not error_found:
            print("✅ 没有发现主要错误")
        
        # 检查生成的文件
        print("\n=== 文件检查 ===")
        outputs_dir = "outputs/silicon_analysis_plots"
        if os.path.exists(outputs_dir):
            # 找到最新的输出目录
            subdirs = [d for d in os.listdir(outputs_dir) if os.path.isdir(os.path.join(outputs_dir, d))]
            if subdirs:
                latest_dir = max(subdirs)
                latest_path = os.path.join(outputs_dir, latest_dir)
                
                # 检查 Stacking 相关文件
                stacking_files = [
                    "stacking_level1_base_learners_shap.png",
                    "shap_summary_plot_bar_Stacking_Overall.png",
                    "shap_summary_plot_dot_Stacking_Overall.png",
                    "Stacking_Overall_partial_dependence_plots.png"
                ]
                
                for file in stacking_files:
                    file_path = os.path.join(latest_path, file)
                    if os.path.exists(file_path):
                        print(f"✅ 文件存在: {file}")
                    else:
                        print(f"❌ 文件不存在: {file}")
        
        if result.returncode == 0:
            print("\n🎉 分析脚本运行成功！")
            return True
        else:
            print("\n❌ 分析脚本运行失败")
            if result.stderr:
                print("错误输出:")
                print(result.stderr[-1000:])  # 显示最后1000个字符
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 脚本运行超时")
        return False
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False

if __name__ == "__main__":
    success = test_stacking_fix()
    if success:
        print("\n✅ Stacking 模型解释修复成功！")
    else:
        print("\n⚠️ 仍有问题需要解决")
