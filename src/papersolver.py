import random
import string
from utils import *
from tools import *
from copy import copy
from inference import *
from pathlib import Path
from copy import deepcopy
from common_imports import *
from agents import get_score
from abc import abstractmethod
import re

from contextlib import contextmanager
import sys, os

@contextmanager
def suppress_stdout():
    with open(os.devnull, "w") as devnull:
        old_stdout = sys.stdout
        sys.stdout = devnull
        try:
            yield
        finally:
            sys.stdout = old_stdout

class Command:
    def __init__(self):
        self.cmd_type = "OTHER"

    @abstractmethod
    def docstring(self) -> str:
        pass

    @abstractmethod
    def execute_command(self, *args) -> str:
        pass

    @abstractmethod
    def matches_command(self, cmd_str) -> bool:
        pass

    @abstractmethod
    def parse_command(self, *args) -> tuple:
        pass


def execute_latex():
    return True


"""
@@@@@@@@@@@@@@@@@@
@@ SEARCH TOOLS @@
@@@@@@@@@@@@@@@@@@
"""

class Arxiv(Command):
    def __init__(self):
        super().__init__()
        self.arxiv_eng = ArxivSearch()
        self.num_papers_per_search = 10
        self.cmd_type = "SEARCH-arxiv"

    def docstring(self) -> str:
        return (
            "============= ARXIV SEARCH TOOL ============="
            "You also have access to machine learning paper from Arxiv. "
            "To search for summaries of papers on arxiv you can use the following command: ```SUMMARY\n<search query>\n```\n where <search query> is a string that will be used as the search query to find papers with semantically similar content and SUMMARY is just the word SUMMARY.\n"
            "To get the full paper text for an arXiv paper, use the following command: ```FULL_TEXT\n<arxiv paper id>\n```\n where <arxiv paper id> is the ID of the arXiv paper (which can be found by using the SUMMARY command), and FULL_TEXT is just the word FULL_TEXT. Make sure to read the full text using the FULL_TEXT command before adding it to your list of relevant papers.\n"
            "When you read arxiv paper, make sure to take note of the techniques they are using to solve their problem as well as the hyperparameters and implementation details. These are very important for successfully solving machine learning problems."
        )

    def execute_command(self, *args) -> str:
        # args[0] -> command
        # args[1] -> query
        if args[0] == "SUMMARY":
            return self.arxiv_eng.find_papers_by_str(args[1], self.num_papers_per_search)
        elif args[0] == "FULL_TEXT":
            return self.arxiv_eng.retrieve_full_paper_text(args[1])
        raise Exception("Invalid Arxiv Search")

    def matches_command(self, cmd_str) -> bool:
        if "```SUMMARY" in cmd_str: return True
        elif "```FULL_TEXT" in cmd_str: return True
        return False

    def parse_command(self, *args) -> tuple:
        sum_text = extract_prompt(args[0], "SUMMARY")
        full_text = extract_prompt(args[0], "FULL_TEXT")
        
        sum_text = sum_text.split("\n") if sum_text is not None else []
        full_text = full_text.split("\n") if full_text is not None else []
        
        if len(sum_text) == 0 and len(full_text) == 0: return False, None
        if len(sum_text) > 0: return True, ("SUMMARY", sum_text,)
        if len(full_text) > 0: return True, ("FULL_TEXT", full_text,)


"""
@@@@@@@@@@@@@@@@@@@
@@ WRITING TOOLS @@
@@@@@@@@@@@@@@@@@@@
"""

class PaperReplace(Command):
    def __init__(self):
        super().__init__()
        self.cmd_type = "PAPER-replace"

    def docstring(self) -> str:
        return (
            "============= PAPER REPLACING TOOL =============\n"
            "You also have access to a paper replacing tool. \n"
            "This tool allows you to entirely re-write/replace all of the current document content and erase all existing content.\n"
            "You can use this tool via the following command: ```REPLACE\n<content here>\n```, where REPLACE is the word REPLACE and <content here> will be the new content that is replacing the entire document. This content will be automatically converted to Markdown format. This tool is useful if you want to make very significant changes, such as entirely changing the model, or the learning process. Before changing the existing content to be your new content, your new content will be tested and if it returns an error it will not replace the existing content. Try limiting the use of rewriting and aim for editing the content more."
        )

    def execute_command(self, *args) -> str:
        # args[0] -> new content
        args = args[0]
        return args[0]

    def matches_command(self, cmd_str) -> bool:
        if "```REPLACE" in cmd_str: return True
        return False

    def parse_command(self, *args) -> tuple:
        new_content = extract_prompt(args[0], "REPLACE")
        if new_content is None:
            return False, (None, "Error: No REPLACE block found in the response.")
        # 使用Markdown编译而不是DOCX
        from utils import compile_markdown
        markdown_ret = compile_markdown(new_content, "research_paper.md")
        if "[MARKDOWN COMPILATION ERROR]" in markdown_ret: 
            return False, (None, markdown_ret,)
        return True, (new_content.split("\n"), markdown_ret)


class PaperEdit(Command):
    def __init__(self):
        super().__init__()
        self.cmd_type = "PAPER-edit"

    def docstring(self) -> str:
        return (
            "============= PAPER EDITING TOOL =============\n"
            "You also have access to a paper editing tool. \n"
            "This tool allows you to replace lines indexed n through m (n:m) of the current document content with as many lines of new content as you want to add. This removal is inclusive meaning that line n and m and everything between n and m is removed. This will be the primary way that you interact with the document. \n"
            "You can edit the document using the following command: ```EDIT N M\n<new lines to replace old lines>\n``` EDIT is the word EDIT, N is the first line index you want to replace and M the the last line index you want to replace (everything inbetween will also be removed), and <new lines to replace old lines> will be the new content that is replacing the old content. Before changing the existing content to be your new content, your new content will be tested and if it returns an error it will not replace the existing content. Your changes should significantly change the content. You should write new paragraphs and update old ones. Try using the edit command often. Make sure to generate lots of text. You should also avoid editing lines 0 0, and should edit the main text of the paragraphs, such as editing lines in the middle of the text body."
        )

    def execute_command(self, *args) -> str:
        # args[0] -> N (int)
        # args[1] -> M (int)
        # args[2] -> old content
        # args[3] -> new lines to replace
        try:
            args = args[0]
            current_content = args[2]
            lines_to_add = list(reversed(args[3]))
            lines_to_replace = list(reversed(range(args[0], args[1]+1)))
            for _ln in lines_to_replace:
                current_content.pop(_ln)
            for _line in lines_to_add:
                current_content.insert(args[0], _line)
            new_content = "\n".join(current_content)

            # 生成Markdown文件
            from utils import compile_markdown
            markdown_ret = compile_markdown(new_content, "research_paper.md")

            # 确保markdown_ret不为None
            if markdown_ret is None:
                markdown_ret = "Markdown compilation returned None - treating as success"

            if "error" in markdown_ret.lower():
                return (False, None, markdown_ret)
            return (True, current_content, markdown_ret)
        except Exception as e:
            return (False, None, str(e))

    def matches_command(self, cmd_str) -> bool:
        if "```EDIT" in cmd_str: return True
        return False

    def parse_command(self, *args) -> tuple:
        cmd_str, content_lines = args[0], args[1]
        success = True
        try:
            text = extract_prompt(cmd_str, "EDIT").split("\n")
            if len(text) == 0: return False, (None, None, None, None)
            lines_to_edit = text[0].split(" ")
            if len(lines_to_edit) != 2: return False, (None, None, None, None)
            lines_to_edit = [int(_) for _ in lines_to_edit]
            if len(text[1:]) == 0: return False, (None, None, None, None)
            return success, (lines_to_edit[0], lines_to_edit[1], content_lines, text[1:])
        except Exception as e:
            return False, (None, None, None, None)




# Modified version of section tips from the AI scientist paper!
# Good work guys :) https://github.com/SakanaAI/AI-Scientist/blob/main/ai_scientist/perform_writeup.py
per_section_tips = {
    "abstract": """
CRITICAL: Keep the abstract to exactly 150-200 words maximum. Be concise and focused.

Structure (write as ONE continuous paragraph):
- Background (1-2 sentences): Brief context and problem statement
- Methods (1-2 sentences): Key machine learning approaches used
- Results (1-2 sentences): Main quantitative findings with specific metrics
- Conclusions (1 sentence): Primary contribution and impact

Style guidelines:
- Use simple notation: R² instead of complex LaTeX formulas
- Avoid excessive technical jargon
- Focus only on the most important findings
- Write for a broad scientific audience
- Keep mathematical notation minimal and readable

Example target length: "This study presents a machine learning framework for silicon extraction optimization from fly ash. We employed ensemble methods including Random Forest and Stacking models with SHAP interpretability analysis. The Stacking model achieved R² = 0.89 with key insights into temperature-alkali interactions. This approach enables efficient process optimization for sustainable materials engineering."

Write a similarly concise abstract. This must be one continuous paragraph with no line breaks.
""",
    "introduction": """
- Longer version of the Abstract, i.e. of the entire paper
- What are we trying to do and why is it relevant?
- Why is this hard? 
- How do we solve it (i.e. our contribution!)
- How do we verify that we solved it (e.g. Experiments and results)
- New trend: specifically list your contributions as bullet points
- Extra space? Future work!
""",
    "related work": """
- Academic siblings of our work, i.e. alternative attempts in literature at trying to solve the same problem. 
- Goal is to “Compare and contrast” - how does their approach differ in either assumptions or method? If their method is applicable to our Problem Setting I expect a comparison in the experimental section. If not, there needs to be a clear statement why a given method is not applicable. 
- Note: Just describing what another paper is doing is not enough. We need to compare and contrast.
""",
    "background": """
- Academic Ancestors of our work, i.e. all concepts and prior work that are required for understanding our method. 
- Usually includes a subsection, Problem Setting, which formally introduces the problem setting and notation (Formalism) for our method. Highlights any specific assumptions that are made that are unusual. 
- Make sure to use mathematical notation when necessary.
- Note: If our paper introduces a novel problem setting as part of its contributions, it's best to have a separate Section.
""",
    "methods": """
- What we do. Why we do it. All described using the general Formalism introduced in the Problem Setting and building on top of the concepts / foundations introduced in Background.
- Make sure you clearly report precise mathematical equations in the methods section and the precise methodology.
""",
    "experimental setup": """
- How do we test that our stuff works? Introduces a specific instantiation of the Problem Setting and specific implementation details of our Method for this Problem Setting.
- Do not imagine unknown hardware details.
- Includes a description of the dataset, evaluation metrics, important hyperparameters, and implementation details.
""",
    "results": """
- Shows the results of running Method on our problem described in Experimental Setup.
- Includes statements on hyperparameters and other potential issues of fairness.
- Only includes results that have actually been run and saved in the logs. Do not hallucinate results that don't exist.
- Make sure you clearly and numerically report experimental results in the results section.
- If results exist: compares to baselines and includes statistics and confidence intervals. 
- If results exist: includes ablation studies to show that specific parts of the method are relevant.
- Discusses limitations of the method.
- Make sure to include all the results from the experiments, and include all relevant figures.
""",
    "discussion": """
- Brief recap of the entire paper.
- To keep going with the analogy, you can think of future work as (potential) academic offspring.
""",
}

class PaperSolver:
    def __init__(self, llm_str, notes=None, max_steps=10, insights=None, plan=None, exp_code=None, exp_results=None, lit_review=None, ref_papers=None, topic=None, openai_api_key=None, openai_base_url=None, openai_model_name=None, compile_pdf=True, author_name="Agent Laboratory", generated_images=None, existing_report_path=None):
        if notes is None: self.notes = []
        else: self.notes = notes
        if plan is None: self.plan = ""
        else: self.plan = plan
        self.author_name = author_name # Add author_name
        if exp_code is None: self.exp_code = ""
        else: self.exp_code = exp_code
        if exp_results is None: self.exp_results = ""
        else: self.exp_results = exp_results
        if lit_review is None: self.lit_review = ""
        else: self.lit_review = lit_review
        if insights is None: self.insights = ""
        else: self.insights = insights
        if ref_papers is None: self.ref_papers = ""
        else: self.ref_papers = ref_papers
        if topic is None: self.topic = ""
        else: self.topic = topic
        self.compile_pdf = compile_pdf
        self.llm_str = llm_str
        self.model = llm_str # Add this line to initialize self.model
        self.notes = notes
        self.max_papers = 1
        self.st_hist_len = 10
        self.min_gen_trials = 2
        self.max_steps = max_steps
        self.document_lines = []
        self.prev_document_ret = str()
        self.section_related_work = {}
        self.openai_api_key = openai_api_key
        self.openai_base_url = openai_base_url
        self.openai_model_name = openai_model_name
        self.generated_images = generated_images if generated_images else [] # Store generated images
        self.existing_report_path = existing_report_path

        # 初始化commands列表
        self.commands = []

    def solve(self):
        num_attempts = 0
        best_pkg = None
        top_score = None
        self.prev_document_ret = None
        while True:
            self.document_lines = copy(random.choice(self.best_report)[0])
            model_resp = query_model(
                model_str=self.model,
                system_prompt=self.system_prompt(),
                prompt=f"\nNow please enter a command: ",
                temp=1.0,
                openai_api_key=self.openai_api_key,
                openai_base_url=self.openai_base_url,
                openai_model_name=self.openai_model_name)
            #print(model_resp)
            model_resp = self.clean_text(model_resp)
            cmd_str, document_lines, prev_document_ret, score = self.process_command(model_resp)
            if score is not None:
                if top_score is None:
                    best_pkg = copy(document_lines), copy(prev_document_ret), copy(model_resp), copy(cmd_str)
                    top_score = score
                elif score > top_score:
                    best_pkg = copy(document_lines), copy(prev_document_ret), copy(model_resp), copy(cmd_str)
                    top_score = score
            if num_attempts >= self.min_gen_trials and top_score is not None: break
            print(f"@@@ Command Exec // Attempt {num_attempts}: ", str(cmd_str).replace("\n", " | "))
            print(f"$$$ Score: {score}")
            num_attempts += 1
        self.document_lines, self.prev_document_ret, model_resp, cmd_str = best_pkg
        # add top scoring paper that was successful to the best papers
        if top_score > self.best_report[-1][1]:
            # replace the lowest scoring one
            if len(self.best_report) >= self.max_papers:
                self.best_report.pop(-1)
            self.best_report.append((copy(self.document_lines), copy(top_score), self.prev_document_ret))
            # sort by score, to make sure lowest are removed in future
            self.best_report.sort(key=lambda x: x[1], reverse=True)
        return model_resp, cmd_str

    def initial_solve(self):
        """
        Initialize the solver and get an initial set of papers and a return
        @return: None
        """
        if self.existing_report_path and os.path.exists(self.existing_report_path):
            print(f"🔄 Loading existing report from {self.existing_report_path}...")
            with open(self.existing_report_path, 'r', encoding='utf-8') as f:
                self.document_lines = [line.rstrip('\n') for line in f.readlines()] # Load existing lines, remove trailing newlines
            
            # Clean up the loaded content: remove old image paths and empty lines that might cause issues.
            # This is a heuristic to prepare for clean re-insertion by LLM.
            cleaned_lines = []
            for line in self.document_lines:
                # Remove lines containing only image markdown, but keep figure captions.
                # A simple regex to detect image-only lines
                if re.match(r'!\[.*?\]\(\.\./silicon_analysis_plots_.*?\.png\)', line.strip()):
                    continue
                if line.strip() != "": # Remove purely empty lines
                    cleaned_lines.append(line)
            self.document_lines = cleaned_lines
            
            self.best_score = self.calculate_initial_score(self.document_lines) # Calculate score for existing doc
            self.best_report = [(copy(self.document_lines), self.best_score, "Loaded existing report")]
            print(f"✅ Existing report loaded with initial score: {self.best_score}")
            # Set initial commands to PaperEdit, as we'll be editing, not generating from scratch
            self.commands = [PaperEdit()]
            self.prev_working_report = copy(self.document_lines)
        else:
            # Original logic for generating from scratch
            self.best_score = None
            self.commands = [PaperReplace()]
            self.model = f"{self.llm_str}"
            init_report, init_return, self.best_score = self.gen_initial_report()
            self.best_report = [(copy(init_report), self.best_score, init_return) for _ in range(1)]
            self.document_lines = init_report
            self.model = f"{self.llm_str}"
            self.commands = [PaperEdit()] #, Replace()]
            self.prev_working_report = copy(self.document_lines)

    def calculate_initial_score(self, document_lines):
        # Implement a basic scoring for the loaded document, or return a default.
        # For simplicity, could return 0 or call get_score if suitable.
        try:
            score, _, _ = get_score(self.plan, "\n".join(document_lines), reward_model_llm=self.llm_str, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)
            return score
        except Exception as e:
            print(f"⚠️ Error calculating initial score for existing report: {e}")
            return 0.0 # Default score if calculation fails

    @staticmethod
    def clean_text(text):
        text = text.replace("```\n", "```")
        return text

    def gen_initial_report(self):
        num_attempts = 0
        arx = ArxivSearch()

        # If an existing report is loaded, use it as the base for current_document_lines
        # Otherwise, start with an empty list for scaffold generation.
        current_document_lines = self.document_lines if self.existing_report_path and self.document_lines else []
        
        # Define the order of sections, including the initial scaffold generation
        sections_to_generate = ["scaffold", "abstract", "introduction", "related work", "background", "methods", "experimental setup", "results", "discussion"]
        
        for _section in sections_to_generate:
            section_complete = False
            
            # Check if section already exists and has content (not just a placeholder)
            section_content_exists = False
            if current_document_lines:
                # A simple heuristic to check for existing section content:
                # Find the section header, then check if there's substantial text after it
                # before the next section header or end of document.
                section_header_pattern = re.compile(r'^(#+)\s*' + re.escape(_section.title()) + r'\s*$', re.IGNORECASE)
                next_section_header_pattern = re.compile(r'^(#+)\s*\d+\.\s*[A-Za-z\s]+\s*$', re.IGNORECASE) # General pattern for any section header
                
                section_start_line = -1
                for i, line in enumerate(current_document_lines):
                    if section_header_pattern.search(line):
                        section_start_line = i
                        break
                
                if section_start_line != -1:
                    # Check lines after the header for actual content
                    content_found = False
                    for i in range(section_start_line + 1, len(current_document_lines)):
                        line = current_document_lines[i].strip()
                        if line and not next_section_header_pattern.search(line) and not re.match(r'\[[A-Z\s]+ HERE\]', line):
                            # Found non-empty, non-placeholder, non-header content
                            content_found = True
                            break
                        if next_section_header_pattern.search(line): # Reached next section
                            break
                    
                    if content_found:
                        section_content_exists = True
            
            if section_content_exists and _section != "scaffold": # Scaffold always needs to be generated or replaced if not empty
                print(f"--- [INFO] Section '{_section}' already has content. Skipping generation.")
                section_complete = True
            if not section_complete:
                # For sections that might need literature search
                if _section in ["introduction", "related work", "background", "methods", "discussion"]:
                    attempts = 0
                    papers = str()
                    first_attempt = True
                    while len(papers) == 0:
                        att_str = str()
                        if attempts > 5: # Limit search attempts
                            break
                        if not first_attempt:
                            att_str = "This is not your first attempt, please try to come up with a simpler search query."
                        
                        # Query model for search query
                        search_query_prompt = f"Given the following research topic {self.topic} and research plan: \n\n{self.plan}\n\nPlease come up with a search query to find relevant papers on arXiv. Respond only with the search query and nothing else. This should be a a string that will be used to find papers with semantically similar content. {att_str}"
                        system_prompt_search = f"You are a research paper finder. You must find papers for the section {_section}. Query must be text nothing else."
                        
                        if self.llm_str == "openai-compatible":
                            search_query = query_model(model_str=self.llm_str, prompt=search_query_prompt, system_prompt=system_prompt_search, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)
                        else:
                            search_query = query_model(model_str=self.llm_str, prompt=search_query_prompt, system_prompt=system_prompt_search, openai_api_key=self.openai_api_key)
                        
                        search_query = search_query.replace('"', '').strip()
                        if search_query:
                            papers = arx.find_papers_by_str(query=search_query, N=10)
                        else:
                            print(f"Warning: No search query generated for section {_section}. Skipping paper search.")
                            papers = "" # Ensure papers is an empty string if no query
                            
                        first_attempt = False
                        attempts += 1
                    if len(papers) != 0:
                        self.section_related_work[_section] = papers
                
                # Generate content for the current section
                num_attempts_section = 0 # Track attempts for current section generation
                while not section_complete and num_attempts_section < self.max_steps * 2: # Limit attempts for content generation
                    err = str()
                    if num_attempts_section == 0: err = str()
                    else: err = f"The following was the previous command generated: {model_resp}. This was the error return {cmd_str}. You should make sure not to repeat this error and to solve the presented problem."
                    
                    rp = str()
                    if _section in self.section_related_work:
                        rp = f"Here are related papers you can cite: {self.section_related_work[_section]}. You can cite them just by putting the arxiv ID in parentheses, e.g. (arXiv 2308.11483v1)\n"
                    
                    # Determine the prompt for content generation
                    prompt_content = f"{err}\n{rp}\nNow please enter the command to create the designated section. Make sure to only write the text for that section and nothing else. Do not include packages or section titles, just the section content."
                    
                    if _section == "scaffold":
                        prompt_content = f"{err}\nNow please enter the ```REPLACE command to create the scaffolding:\n "
                    else:
                        # For subsequent sections, we are generating content that will be EDITED into the document
                        prompt_content = f"{err}\n{rp}\nNow please generate comprehensive content for the {_section} section. CRITICAL REQUIREMENTS:\n"
                        prompt_content += f"1. You MUST write substantial, detailed content (minimum 300 words)\n"
                        prompt_content += f"2. Include specific technical details, methodologies, and results\n"
                        prompt_content += f"3. If this is the results section, include specific numerical results and analysis\n"
                        prompt_content += f"4. Use proper academic writing style with technical terminology\n" # Removed image insertion hint
                        prompt_content += f"5. IMPORTANT: You MUST encapsulate your entire response within a ```SECTION_CONTENT\\n<content here>\\n``` block.\n"
                        prompt_content += f"The content should be comprehensive and detailed, aiming for substantial length."
                        
                    # Pass openai_base_url and openai_model_name if model is openai-compatible
                    if self.model == "openai-compatible":
                        model_resp = query_model(
                            model_str=self.model,
                            system_prompt=self.system_prompt(section=_section),
                            prompt=prompt_content,
                            temp=0.8,
                            openai_api_key=self.openai_api_key,
                            openai_base_url=self.openai_base_url,
                            openai_model_name=self.openai_model_name
                        )
                    else:
                        model_resp = query_model(
                            model_str=self.model,
                            system_prompt=self.system_prompt(section=_section),
                            prompt=prompt_content,
                            temp=0.8,
                            openai_api_key=self.openai_api_key
                        )
                    
                    print(f"DEBUG: model_resp for {_section}:\n{model_resp}")
                    model_resp = self.clean_text(model_resp)
                    
                    if _section == "scaffold":
                        # Initial scaffold generation
                        new_text = extract_prompt(model_resp, "REPLACE")
                        if new_text is None:
                            cmd_str = "Error: No REPLACE block found for scaffold. Please ensure the response contains a properly formatted ```REPLACE block."
                            print("@@@ INIT ATTEMPT:", cmd_str)
                            num_attempts_section += 1
                            continue
                        
                        # Perform the initial replace command
                        cmd_str, document_lines, prev_document_ret, score = self.process_command(model_resp, scoring=False)
                        if score is not None:
                            current_document_lines = document_lines # Update the main document lines
                            section_complete = True
                            print("$"*10, f"SCAFFOLD [{_section}] CREATED", "$"*10)
                        else:
                            print(f"@@@ INIT ATTEMPT: Scaffold creation FAILED: {cmd_str}")
                            num_attempts_section += 1
                            
                    else: # For all other sections (abstract, introduction, etc.)
                        new_section_content = extract_prompt(model_resp, "SECTION_CONTENT")
                        if new_section_content is None:
                            cmd_str = f"Error: No SECTION_CONTENT block found in model response for section {_section}. Please ensure the response contains a properly formatted ```SECTION_CONTENT block."
                            print("@@@ INIT ATTEMPT:", cmd_str)
                            num_attempts_section += 1
                            continue
                        
                        # Find the line number of the placeholder in the current document
                        placeholder = f"[{_section.upper()} HERE]"
                        start_line_idx = -1
                        for i, line in enumerate(current_document_lines):
                            if placeholder in line:
                                start_line_idx = i
                                break
    
                        if start_line_idx == -1:
                            # 尝试其他可能的占位符格式
                            alternative_placeholders = [
                                f"[{_section.upper()}]",
                                f"{_section.upper()} HERE",
                                f"## {_section.title()}",
                                f"# {_section.title()}"
                            ]
    
                            for alt_placeholder in alternative_placeholders:
                                for i, line in enumerate(current_document_lines):
                                    if alt_placeholder in line:
                                        start_line_idx = i
                                        break
                                if start_line_idx != -1:
                                    break
    
                            if start_line_idx == -1:
                                # 如果还是找不到，就在文档末尾添加
                                print(f"Warning: Could not find placeholder for section {_section}. Adding at the end.")
                                current_document_lines.append(f"\n## {_section.title()}\n")
                                current_document_lines.append(new_section_content)
                                section_complete = True
                                continue # Move to next section
                        
                        # Construct the EDIT command dynamically
                        # N M\n<new lines to replace old lines>
                        edit_command_str = f"{start_line_idx} {start_line_idx}\n{new_section_content}"
                        model_resp_for_edit = '```EDIT\n' + edit_command_str + '\n```'
                        
                        # Temporarily add PaperEdit to commands if not already there
                        if not any(isinstance(cmd, PaperEdit) for cmd in self.commands):
                            self.commands.append(PaperEdit())
                        
                        # Process the EDIT command
                        cmd_str, document_lines_after_edit, prev_document_ret, score = self.process_command(model_resp_for_edit, scoring=False)
                        
                        if score is not None: # Assuming score indicates success for now
                            current_document_lines = document_lines_after_edit # Update the main document lines
                            section_complete = True
                            print("$"*10, f"SCAFFOLD [{_section}] CREATED", "$"*10) # Re-use the existing print for consistency
                        else:
                            print(f"@@@ INIT ATTEMPT: Section {_section} edit FAILED: {cmd_str}")
                            num_attempts_section += 1
                        
                num_attempts_section += 1 # Increment attempts for this section
                # If a section fails to complete after max_attempts, break to avoid infinite loop
                if num_attempts_section >= self.max_steps * 2: # Give it more attempts for content generation
                    print(f"Warning: Max attempts reached for section {_section}. Moving to next section.")
                    section_complete = True # Force completion to move on
            
            # Update the main document_lines after each section is complete (or max attempts reached)
            self.document_lines = current_document_lines
 
        print("$"*10, "ALL SCAFFOLDS FILLED", "$"*10) # Changed from "SCAFFOLD CREATED"
        # The final document_lines is what we want to return
        # The score and prev_document_ret from the last successful command (likely the last EDIT)
        final_score = score
        final_prev_document_ret = prev_document_ret
        return self.document_lines, final_prev_document_ret, final_score

    def process_command(self, model_resp, scoring=True):
        """
        Take command from language model and execute if valid
        @param model_resp: (str) language model output
        @return: (tuple) tuple containing the following items
            - cmd_str: (str) document execution return and success flag
            - document_lines: (list) list of document lines as strings
            - prev_document_ret: (str) output from running document
            - score: (float) score of model
        """
        cmd_str = None
        score = None
        prev_document_ret = self.prev_document_ret
        document_lines = copy(self.document_lines)
        if "\\includegraphics[width=\\textwidth]{Figure_1.png}" in model_resp or "\\includegraphics[width=\\textwidth]{Figure_2.png}" in model_resp:
            cwd = os.getcwd()
            model_resp = model_resp.replace("\\includegraphics[width=\\textwidth]{Figure_1.png}", "\\includegraphics[width=\\textwidth]{" + cwd + "/Figure_1.png}")
            model_resp = model_resp.replace("\\includegraphics[width=\\textwidth]{Figure_2.png}", "\\includegraphics[width=\\textwidth]{" + cwd + "/Figure_2.png}")
        for cmd in self.commands:
            if cmd.matches_command(model_resp):
                # attempt to execute the document edit command
                if cmd.cmd_type == "PAPER-edit": # DONE
                    score = None
                    failed = True
                    success, args = cmd.parse_command(model_resp, document_lines)
                    if success:
                        # 确保args[1]不为None
                        initial_ret = args[1] if args[1] is not None else "Initial document state"
                        document_err = f"Return from executing document: {initial_ret}"

                        # True, current_document, document_ret
                        args = cmd.execute_command((args[0], args[1], document_lines, args[3], self.compile_pdf))
                        success = success and args[0]
                        if not success:
                            # 确保错误信息不为None
                            error_msg = args[2] if args[2] is not None else "Unknown error during document execution"
                            document_err += f"\nExecution failed: {error_msg}"
                        else:
                            document_lines = copy(args[1]) #
                            if scoring:
                                score, cmd_str, is_valid = get_score(self.plan, "\n".join(document_lines), reward_model_llm=self.llm_str, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)
                            else:
                                score, cmd_str, is_valid = 0.0, "Document scored successfully", True
                            if is_valid: failed = False
                            # 确保cmd_str不为None
                            final_ret = cmd_str if cmd_str is not None else "Document processing completed"
                            document_err += f"\nReturn from executing document: {final_ret}"
                        print("$$$$ DOCUMENT EDIT (success)")
                    else:
                        # Handle parsing failure
                        document_err = "Failed to parse EDIT command from model response"

                    if failed:
                        cmd_str = f"Document edit FAILED due to the following error: {document_err}.  Document was reverted back to original state before edits."
                        print("$$$$ DOCUMENT EDIT (failed)")
                    else:
                        cmd_str = "Document was successfully edited."
                        document_lines = copy(args[1])
                        prev_document_ret = copy(args[2])
                        print("$$$$ DOCUMENT EDIT (success)")
                elif cmd.cmd_type == "PAPER-replace": # DONE
                    score = None
                    failed = True
                    success, args = cmd.parse_command(model_resp, self.compile_pdf)
                    if success:
                        document_err = f"Return from executing document: {args[1]}"
                        document_lines = copy(args[0]) #
                        if scoring:
                            score, cmd_str, is_valid = get_score(self.plan, "\n".join(document_lines), reward_model_llm=self.llm_str, openai_api_key=self.openai_api_key, openai_base_url=self.openai_base_url, openai_model_name=self.openai_model_name)
                        else:
                            score, cmd_str, is_valid = 0.0, "Document scored successfully", True
                        if is_valid: failed = False
                        document_err += f"\nReturn from executing document: {cmd_str}"
                    else:
                        # Handle parsing failure
                        document_err = "Failed to parse REPLACE command from model response"

                    if failed:
                        cmd_str = f"Document replacement FAILED due to the following error: {document_err}.  Document was reverted back to original state before edits."
                        print("$$$$ DOCUMENT REPLACE (failed)")
                    else:
                        cmd_str = "Document was successfully replaced."
                        document_lines = copy(args[0])
                        prev_document_ret = copy(args[1])
                        print("$$$$ DOCUMENT REPLACE (success)")
        return cmd_str, document_lines, prev_document_ret, score

    def generate_document_lines(self, content):
        """
        Generate well-formatted content lines with line numbers
        @param content: (list) list of content line strings
        @return: (str) content lines formatted with line numbers
        """
        content_str = str()
        for _index in range(len(content)):
            content_str += f"{_index} |{content[_index]}\n"
        return content_str

    def system_prompt(self, commands=True, section=None):
        """
        Produce a system prompt for the document-solver
        @param commands: (bool) whether to use command prompt
        @return: (str) system prompt
        """
        if section == "abstract": length = "This section should be ONLY 1 paragraph and exactly 150-200 words maximum. Be concise and focused."
        else: length = "This section should be approximately 2-4 paragraphs and so your output should be several paragraphs of content."
        methods_str = str() # Keep for other method-specific tips if any, or remove if only for old figures.
        image_insertion_prompt = "" # Will be handled by llm_insert_figures later

        if section is not None and section == "scaffold":
            section_cmd = (
                f"Your objective right now is to only build the scaffolding for the document. "
                f"You should not include any text in the body of the document, but should have an empty scaffold for each of the sections. "
                f"Where the sections go, write [ABSTRACT HERE] for abstract, and write [INTRODUCTION HERE] for the introduction... etc. "
                f"Your document should have the following sections: 1. Abstract 2. Introduction, 3. Background, 4. Related Work 5. Methods, 6. Experimental Setup 7. Results, and 8. Discussion. "
                f"Just create the scaffolding as compilable content. Your title should start with Research Report: [title here] where title here is a title you choose. For author write {self.author_name}."
            )
        elif section is not None:
            section_cmd = (
                f"Your only goal is to generate content for the following {section}. "
                f"DO NOT INCLUDE ANY PACKAGES OR ANY SECTION COMMANDS. DO NOT INCLUDE A TITLE OR DATE ONLY TEXT. "
                f"You only have to generate text for this specific section and do not have to output anything else. "
                f"{length} I repeat DO NOT INCLUDE ANY PACKAGES OR ANY SECTION COMMANDS. DO NOT INCLUDE A TITLE OR DATE ONLY TEXT. "
                f"Use as many equations as you find necessary. You should include mathematical equations, numbers, and tables where necessary. "
                f"Remember that to include a percentage sign % you must add a backslash \\% or else it will become a comment. "
                f"Here are some tips {per_section_tips.get(section, 'Write comprehensive content for this section.')}  {methods_str}\n\n"
                f"IMPORTANT: You MUST encapsulate your entire response within a ```SECTION_CONTENT\\n<content here>\\n``` block. "
                f"For example, if you are writing the abstract, your entire response should be:\n\n"
                f"```SECTION_CONTENT\n"
                f"This paper introduces a novel approach to...\n"
                f"Our method leverages advanced machine learning techniques to...\n"
                f"We demonstrate the effectiveness of our approach through extensive experiments on...\n"
                f"The results show that our method outperforms existing state-of-the-art approaches by a significant margin.\n"
                f"```\n\n"
                f"The content inside the SECTION_CONTENT block should be a comprehensive and detailed write-up for the {section} section, aiming for substantial length to contribute to the overall 4000-word goal. "
                f"Generate at least 400 words for this section if possible."
            )
        else:
            section_cmd = ""
        
        content_len = sum([i.strip(string.punctuation).isalpha() for i in ("".join(self.document_lines)).split()])
        if content_len < 4000:
            content_progress = f"The current length of the document is {content_len} words, you must increase this by {4000-content_len} words. Aim to write long, detailed paragraphs for each section."
        else:
            content_progress = "The document has reached the target length of 4000 words. Focus on refining existing content."
        
        print(content_progress)
        
        cmd_set = f"The following are commands you have access to: {self.command_descriptions()}\n." if commands else ""
        
        ref_papers = ""
        if len(self.ref_papers) > 0:
            refpapers = '\n'.join(self.ref_papers)
            ref_papers = f"Here is a reference paper that is high quality:\n{refpapers}\n\n\n"
        
        lit_review_str = str(self.lit_review)[:20000]
        
        return (
            f"{ref_papers}"
            f"{self.role_description()}.\n"
            f"The following are your task instructions: {self.phase_prompt()}\n"
            f"The following are notes, instructions, and general tips for you: {self.notes}"
            f"The following literature review was provided for the document:\n{lit_review_str}\n"
            f"You are given a document writing task. The original research plan was described as follows: {self.plan}\n"
            f"A team of research wrote the following code, following this plan: {self.exp_code}\n"
            f"After running this code, the following results were observed: {self.exp_results}\n"
            f"Provided was an interpretation of the experimental results:\n{self.insights}\n"
            f"Your writing style should be boring and objective.\n"
            f"Your goal is to write a document as well as possible. You will receive a score after you write the document and should aim to maximize the score by writing a high quality document. The document length should be 7-8 pages or 4000 words in total. It should be comprehensive and well-structured. Remember, the document should be substantial but focused. {content_progress}\n"
            f"{cmd_set}\n"
            f"Provided here is your current document {self.generate_document_lines(self.document_lines)}"
            f"{section_cmd}"
        )

    def command_descriptions(self):
        """
        Provide command descriptions
        @return: (str) command descriptions
        """
        if hasattr(self, 'commands') and self.commands:
            cmd_strings = "\n".join([_cmd.docstring() for _cmd in self.commands])
        else:
            cmd_strings = "No commands available."
        return f"\nYou also have access to tools which can be interacted with using the following structure: ```COMMAND\n<command information here>\n```, where COMMAND is whichever command you want to run (e.g. EDIT,...), <command information here> is information used for the command and ``` are meant to encapsulate the command. ``` must be included as part of the command both at the beginning and at the end of the command. DO NOT FORGOT TO HAVE ``` AT THE TOP AND BOTTOM OF COMMAND. and this structure must be followed to execute a command correctly. YOU CAN ONLY EXECUTE A SINGLE COMMAND AT A TIME! Do not try to perform multiple commands EVER only one." + cmd_strings

    def role_description(self):
        """
        Provide role description
        @return: (str) role description
        """
        return "You are a computer science PhD student at a top university who has submitted their paper to an ML conference called ICLR. Your goal was to write a document and get high scores from the reviewers so that it get accepted to the conference. Your document should be approximately 7-8 pages and around 4000 words. Your article should ONLY CONTAIN EIGHT sections as follows: 1. Abstract 2. Introduction, 3. Background, 4. Related Work 5. Methods, 6. Experimental Setup 7. Results, and 8. Discussion.\n"


    def phase_prompt(self,):
        """
        Describe system role and general tips for mle-solver
        @return: (str) system role
        """
        phase_str = (
            "You are a PhD student who has submitted their paper to an ML conference called ICLR. Your goal was to write a document and get high scores from the reviewers so that it get accepted to the conference.\n"
        )
        return phase_str

    def post_process_report(self, report):
        """
        后处理报告以确保格式正确，使用LLM智能处理
        @param report: (str) 原始报告内容
        @return: (str) 处理后的报告内容
        """
        from utils import clean_latex_for_markdown
        from inference import query_model

        print("🔧 开始智能后处理报告...")

        # 1. 基础格式清理
        cleaned_report = clean_latex_for_markdown(report)

        # 2. 检查是否有占位符需要LLM填充
        placeholders = self.find_placeholders(cleaned_report)

        if placeholders:
            print(f"📝 发现 {len(placeholders)} 个占位符，使用LLM生成内容...")
            cleaned_report = self.llm_fill_placeholders(cleaned_report, placeholders)

        # 3. LLM智能图片插入
        cleaned_report = self.llm_insert_figures(cleaned_report)

        # 4. 格式优化
        cleaned_report = self.llm_format_optimization(cleaned_report)
        
        # 5. 动态生成并添加参考文献
        cleaned_report = self.add_references_section(cleaned_report)

        print("✅ 智能后处理完成")
        return cleaned_report



    def find_placeholders(self, content):
        """查找需要填充的占位符"""
        import re

        placeholders = []
        placeholder_patterns = [
            r'\[([A-Z\s]+) HERE\]',
            r'## \d+\.\s*([A-Za-z\s]+)\s*\n\s*\[',
            r'### \d+\.\d+\s*([A-Za-z\s]+)\s*\n\s*\['
        ]

        for pattern in placeholder_patterns:
            matches = re.findall(pattern, content)
            placeholders.extend(matches)

        return list(set(placeholders))  # 去重

    def llm_fill_placeholders(self, content, placeholders):
        """使用LLM填充占位符内容"""
        from inference import query_model

        for placeholder in placeholders:
            if any(keyword in placeholder.upper() for keyword in ['METHODS', 'EXPERIMENTAL', 'RESULTS', 'DISCUSSION']):

                # 构建针对性的提示
                section_prompt = self.build_section_prompt(placeholder)

                try:
                    # 调用LLM生成内容
                    if self.llm_str == "openai-compatible":
                        generated_content = query_model(
                            model_str=self.llm_str,
                            system_prompt="You are an expert materials science researcher writing a comprehensive research paper.",
                            prompt=section_prompt,
                            temp=0.7,
                            openai_api_key=self.openai_api_key,
                            openai_base_url=self.openai_base_url,
                            openai_model_name=self.openai_model_name
                        )
                    else:
                        generated_content = query_model(
                            model_str=self.llm_str,
                            system_prompt="You are an expert materials science researcher writing a comprehensive research paper.",
                            prompt=section_prompt,
                            temp=0.7,
                            openai_api_key=self.openai_api_key
                        )

                    # 替换占位符
                    placeholder_pattern = f"[{placeholder} HERE]"
                    content = content.replace(placeholder_pattern, generated_content.strip())

                    print(f"✅ 已生成 {placeholder} 章节内容")

                except Exception as e:
                    print(f"⚠️ 生成 {placeholder} 内容时出错: {e}")
                    # 如果LLM调用失败，使用备用内容
                    content = content.replace(f"[{placeholder} HERE]", self.get_fallback_content(placeholder))

        return content

    def build_section_prompt(self, section_name):
        """构建章节特定的提示"""

        base_context = f"""
        Research Topic: {self.topic}
        Research Plan: {self.plan}
        Experimental Code: {self.exp_code[:1000]}...
        Experimental Results: {self.exp_results[:1000]}...
        Key Insights: {self.insights[:500]}...
        """

        section_specific_prompts = {
            "METHODS": f"""
            {base_context}

            Please write a comprehensive Methods section for this research paper. Include:
            1. Data collection and preprocessing methodology
            2. Machine learning models used (Random Forest, Gradient Boosting, Stacking, etc.)
            3. Model evaluation metrics and validation approach
            4. Interpretable AI techniques (SHAP, PDP)
            5. Experimental design considerations

            Write in academic style with proper technical detail. Include subsections (5.1, 5.2, etc.).
            Length: 400-600 words.
            """,

            "EXPERIMENTAL SETUP": f"""
            {base_context}

            Please write a detailed Experimental Setup section. Include:
            1. Materials and sample preparation
            2. Experimental procedures and protocols
            3. Equipment and measurement techniques
            4. Design of experiments and parameter ranges
            5. Reference to figures showing correlations and interactions

            Write in academic style. Include subsections (6.1, 6.2, etc.).
            Length: 300-500 words.
            """,

            "RESULTS": f"""
            {base_context}

            Please write a comprehensive Results section. Include:
            1. Model performance comparison with specific metrics
            2. Feature importance analysis from SHAP
            3. Key findings and statistical significance
            4. Visualization descriptions and interpretations
            5. Validation results and model reliability

            Use specific numbers and metrics where possible. Reference figures appropriately.
            Write in academic style. Include subsections (7.1, 7.2, etc.).
            Length: 500-700 words.
            """,

            "DISCUSSION": f"""
            {base_context}

            Please write a thoughtful Discussion section. Include:
            1. Scientific contributions and novel insights
            2. Practical implications for industry
            3. Limitations and assumptions
            4. Future research directions
            5. Broader impact on sustainable materials engineering

            Connect findings to existing literature and theory.
            Write in academic style. Include subsections (8.1, 8.2, etc.).
            Length: 400-600 words.
            """
        }

        return section_specific_prompts.get(section_name.upper(), f"Please write content for the {section_name} section of this research paper.")

    def get_fallback_content(self, section_name):
        """获取备用内容（如果LLM调用失败）"""
        fallback_contents = {
            "METHODS": "This section describes the methodology used in this research, including data collection, machine learning model implementation, and evaluation procedures.",
            "EXPERIMENTAL SETUP": "This section outlines the experimental design, materials, and procedures used to collect data for the machine learning analysis.",
            "RESULTS": "This section presents the key findings from the machine learning analysis, including model performance metrics and feature importance results.",
            "DISCUSSION": "This section discusses the implications of the findings, limitations of the study, and suggestions for future research."
        }

        return fallback_contents.get(section_name.upper(), f"Content for {section_name} section.")

    def llm_insert_figures(self, content):
        """使用LLM智能决定插入什么图片"""
        from inference import query_model
        import os
        import glob
        import re


        print("📸 LLM开始智能插入图片...")

        # 获取可用图片
        research_dir = "research_dir"
        plot_dirs = glob.glob(os.path.join(research_dir, "silicon_analysis_plots_*"))
        if not plot_dirs:
            print("⚠️ 未找到图片文件夹")
            return content

        latest_plot_dir = sorted(plot_dirs)[-1]
        plot_folder_relative = latest_plot_dir.replace("research_dir/", "")

        # 获取所有可用图片
        available_figures = []
        for img_file in os.listdir(latest_plot_dir):
            if img_file.endswith('.png'):
                img_path = os.path.join(latest_plot_dir, img_file)
                if os.path.exists(img_path):
                    available_figures.append({
                        "file": img_file,
                        "path": f"../{plot_folder_relative}/{img_file}"
                    })

        if not available_figures:
            print("⚠️ 未找到图片文件")
            return content

        # 构建图片信息
        figures_info = "\n".join([
            f"- {fig['file']}: {fig['path']}"
            for fig in available_figures
        ])

        # 定义关键图片和其描述，按章节组织 (与 system_prompt 中的定义保持一致)
        figure_sections_map = {
            "methods": {
                "advanced_correlation_matrix.png": "Advanced Correlation Matrix of Material Parameters",
                "feature_interaction_analysis.png": "Feature Interaction Analysis with Target Variable",
                "target_distribution.png": "Distribution of Effective Silicon Concentration in the dataset",
                "EDA_pairplot.png": "Pairwise Interactions of Top Correlated Features"
            },
            "experimental setup": {
                "advanced_correlation_matrix.png": "Advanced Correlation Matrix of Material Parameters",
                "feature_interaction_analysis.png": "Feature Interaction Analysis with Target Variable",
                "target_distribution.png": "Distribution of Effective Silicon Concentration in the dataset",
                "EDA_pairplot.png": "Pairwise Interactions of Top Correlated Features"
            },
            "results": {
                "model_comparison_results.png": "Model Performance Comparison showing R² scores for different algorithms",
                "model_train_test_r2_comparison.png": "Training vs Testing R² Comparison across models",
                "Stacking_performance_jointplot.png": "Stacking Model Performance: Actual vs Predicted values",
                "CatBoost_performance_jointplot.png": "CatBoost Model Performance: Actual vs Predicted values",
                "XGB_performance_jointplot.png": "XGBoost Model Performance: Actual vs Predicted values",
                "LGBM_performance_jointplot.png": "LightGBM Model Performance: Actual vs Predicted values",
                "KNN_performance_jointplot.png": "K-Nearest Neighbors Model Performance: Actual vs Predicted values",
                "RF_performance_jointplot.png": "Random Forest Model Performance: Actual vs Predicted values",
                "MLP_performance_jointplot.png": "Multilayer Perceptron Model Performance: Actual vs Predicted values",
                "shap_summary_plot_bar_StackingOverall.png": "SHAP Summary (Bar) for Stacking Model: Global Feature Importance",
                "shap_summary_plot_dot_StackingOverall.png": "SHAP Summary (Beeswarm) for Stacking Model: Feature Impact and Distribution",
                "stacking_level1_base_learners_shap.png": "SHAP Values for Stacking Level 1 Base Learners",
                "stacking_level2_meta_learner_shap_bar.png": "SHAP Feature Importance for Stacking Level 2 Meta-Learner",
                "StackingOverall_partial_dependence_plots.png": "Partial Dependence Plots for Stacking Model"
            },
            "discussion": {} # Discussion might not have direct new figures, but can reference existing ones.
        }

        # 获取所有可用图片，并添加章节提示
        figures_with_section_hints = []
        for fig in available_figures:
            section_hint = "General"
            for sec_name, fig_map in figure_sections_map.items():
                if fig['file'] in fig_map:
                    section_hint = sec_name.title()
                    break
            figures_with_section_hints.append(f"- {fig['file']} (Suggested Section: {section_hint}): {fig['path']}")
        
        figures_info_with_hints = "\n".join(figures_with_section_hints)

        figure_insertion_prompt = f"""
        You are an expert academic editor specializing in materials science papers.
        Your task is to intelligently insert the most relevant figures into the provided research paper content.
        CRITICAL: Ensure each figure is inserted ONLY ONCE and is placed in the most appropriate section, close to its first textual reference or where it is most contextually relevant.

        Available Figures (with suggested sections for placement):
        {figures_info_with_hints}

        Instructions for Figure Insertion:
        1.  **Identify Existing Figures**: Scan the `Current paper content` to see if any figures are already present. You MUST avoid re-inserting already present figures.
        2.  **Contextual Placement**: Place each figure after the paragraph where it is first and most relevantly discussed. For example, EDA plots should go in Methods/Experimental Setup, performance plots in Results, SHAP/PDP plots in Results or Discussion.
        3.  **Markdown Format**: Use the exact Markdown format: `![Figure X: Descriptive Alt Text](relative_path_to_image.png)`
            *   `relative_path_to_image.png` MUST be the exact path provided in the `Available Figures` list.
        4.  **Caption**: Immediately after the Markdown image, add a caption: `**Figure X**: Detailed description of the figure content.`
        5.  **Sequential Numbering**: Number figures sequentially starting from Figure 1 throughout the entire paper.
        6.  **Text Reference**: Ensure there is a clear textual reference to the figure in the surrounding paragraph (e.g., "As shown in Figure X, the model performance...").
        7.  **Avoid Duplication**: If a figure is already present, DO NOT insert it again. Focus on filling gaps or placing figures that are currently missing.
        8.  **Prioritize Relevance**: If a figure is listed, but not relevant to any section, you may choose to omit it or find a suitable place.
        9.  **Maintain Flow**: Ensure figure insertion does not break the logical flow of the text.

        Current paper content:
        {content}

        Please return the complete paper content with figures properly inserted, captioned, referenced, and without duplication.
        """

        try:
            if self.llm_str == "openai-compatible":
                enhanced_content = query_model(
                    model_str=self.llm_str,
                    system_prompt="You are an expert at formatting academic papers with proper figure placement.",
                    prompt=figure_insertion_prompt,
                    temp=0.3,
                    openai_api_key=self.openai_api_key,
                    openai_base_url=self.openai_base_url,
                    openai_model_name=self.openai_model_name
                )
            else:
                enhanced_content = query_model(
                    model_str=self.llm_str,
                    system_prompt="You are an expert at formatting academic papers with proper figure placement.",
                    prompt=figure_insertion_prompt,
                    temp=0.3,
                    openai_api_key=self.openai_api_key
                )

            print("✅ LLM已智能插入图片")
            return enhanced_content.strip()

        except Exception as e:
            print(f"⚠️ LLM图片插入失败: {e}")
            return content

    def llm_format_optimization(self, content):
        """使用LLM进行格式优化"""
        from inference import query_model

        # Define the term replacement map based on user feedback
        term_replacement_map = {
            "机械化学处理时间": "成型方式 (`granular`)",
            "NaOH的浓度": "活化剂摩尔比",
            # Add more specific replacements if needed, e.g., for different activators
            "Na2CO3 concentration": "Na2CO3摩尔比",
            "Ca(OH)2 concentration": "Ca(OH)2摩尔比",
        }

        # First, apply the strict, non-negotiable replacements
        for old_term, new_term in term_replacement_map.items():
            content = content.replace(old_term, new_term)

        # Now, create the prompt for the LLM to do the rest of the formatting
        format_prompt = f"""
        You are an expert academic editor. Please optimize the formatting of this research paper with the following instructions:

        1.  **Review and Correct Formatting**:
            *   Fix any remaining mathematical notation issues (ensure proper LaTeX formatting for Markdown).
            *   Standardize all temperature units to °C.
            *   Correct any broken or inconsistent chemical formulas (e.g., ensure Al₂O₃, SiO₂).
            *   Ensure section numbering is sequential and consistent (e.g., 1.0, 1.1, 2.0).
            *   Clean up any other formatting inconsistencies (e.g., extra whitespace, inconsistent list styles).

        2.  **Enhance Academic Style**:
            *   Improve sentence structure for clarity and conciseness.
            *   Ensure a formal, objective, and academic tone throughout the paper.
            *   Check for grammatical errors, typos, and awkward phrasing.

        3.  **Final Polish**:
            *   The final output should be a polished, professional, and publication-ready research paper.

        **IMPORTANT**: The most critical terminology corrections have already been applied. Your primary role now is to handle the formatting, style, and remaining inconsistencies. Do not re-introduce old, incorrect terms.

        Paper content to be optimized:
        ```markdown
        {content}
        ```

        Please return only the fully optimized and polished paper content.
        """

        try:
            # The model call now focuses on formatting and style, not term replacement
            if self.llm_str == "openai-compatible":
                optimized_content = query_model(
                    model_str=self.llm_str,
                    system_prompt="You are an expert academic editor specializing in materials science papers. Your task is to polish and format the provided text.",
                    prompt=format_prompt,
                    temp=0.1, # Low temperature for deterministic formatting
                    openai_api_key=self.openai_api_key,
                    openai_base_url=self.openai_base_url,
                    openai_model_name=self.openai_model_name
                )
            else:
                optimized_content = query_model(
                    model_str=self.llm_str,
                    system_prompt="You are an expert academic editor specializing in materials science papers. Your task is to polish and format the provided text.",
                    prompt=format_prompt,
                    temp=0.1,
                    openai_api_key=self.openai_api_key
                )

            print("✅ LLM格式优化完成")
            return optimized_content.strip()

        except Exception as e:
            print(f"⚠️ LLM格式优化失败，返回手动修正后的内容: {e}")
            return content # Return the content that already had the manual replacements



    def add_references_section(self, content):
        """
        Dynamically adds a references section to the report based on literature found during the process.
        @param content: (str) The main content of the report.
        @return: (str) The report content with the references section added or updated.
        """
        print("📚 Dynamically generating references section...")
        
        # Consolidate all found papers from different sections
        all_papers_text = ""
        for section, papers in self.section_related_work.items():
            all_papers_text += papers + "\n"
            
        if not all_papers_text.strip():
            print("⚠️ No literature found in `section_related_work`. Skipping references section.")
            return content

        # Use regex to find all cited arXiv IDs in the main text
        cited_arxiv_ids = set(re.findall(r'\(arXiv[:\s]*([\d\.]+[v\d]*)\)', content))
        
        if not cited_arxiv_ids:
            print("⚠️ No citations found in the text (e.g., '(arXiv:1234.56789)'). Skipping references section.")
            return content

        print(f"Found {len(cited_arxiv_ids)} unique citations in the text: {cited_arxiv_ids}")

        # Parse the collected literature to build a reference map
        reference_map = {}
        # Regex to capture ID, Title, and Authors from the arxiv summary format
        paper_pattern = re.compile(r"Paper ID: ([\d\.]+[v\d]*)\s+Title: (.*?)\s+Authors: <AUTHORS>
        matches = paper_pattern.findall(all_papers_text)
        
        for arxiv_id, title, authors in matches:
            if arxiv_id in cited_arxiv_ids and arxiv_id not in reference_map:
                # Clean up authors list
                authors = authors.strip().replace('\n', ' ')
                title = title.strip().replace('\n', ' ')
                reference_map[arxiv_id] = {
                    "title": title,
                    "authors": authors
                }

        if not reference_map:
            print("⚠️ Could not match cited IDs to the literature found. No references will be generated.")
            return content

        # Build the reference list string
        references_list = []
        for i, arxiv_id in enumerate(sorted(list(reference_map.keys())), 1):
            paper = reference_map[arxiv_id]
            # Simple formatting, can be improved
            authors_formatted = paper['authors'].split(', ')[0] + " et al." if len(paper['authors'].split(',')) > 2 else paper['authors']
            # Assuming a generic year for now, as it's not in the summary
            year = "2023" 
            ref_string = f"[{i}] {authors_formatted}. ({year}). {paper['title']}. *arXiv preprint arXiv:{arxiv_id}*."
            references_list.append(ref_string)

        references_section = "## References\n\n" + "\n\n".join(references_list)

        # Replace or append the references section
        if "## References" in content:
            # Replace existing references section
            content = re.sub(r'## References[\s\S]*', references_section, content)
            print("✅ References section updated.")
        else:
            # Append references section if not exists
            content += "\n\n" + references_section
            print("✅ References section added.")
        
        return content
