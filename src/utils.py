import os, re
import shutil
import tiktoken
import google.generativeai as genai
import subprocess
from docx import Document


def compile_latex(latex_code, compile=True, output_filename="output.pdf", timeout=30):
    latex_code = latex_code.replace(
        r"\documentclass{article}",
        "\\documentclass{article}\n\\usepackage{amsmath}\n\\usepackage{amssymb}\n\\usepackage{array}\n\\usepackage{algorithm}\n\\usepackage{algorithmicx}\n\\usepackage{algpseudocode}\n\\usepackage{booktabs}\n\\usepackage{colortbl}\n\\usepackage{color}\n\\usepackage{enumitem}\n\\usepackage{fontawesome5}\n\\usepackage{float}\n\\usepackage{graphicx}\n\\usepackage{hyperref}\n\\usepackage{listings}\n\\usepackage{makecell}\n\\usepackage{multicol}\n\\usepackage{multirow}\n\\usepackage{pgffor}\n\\usepackage{pifont}\n\\usepackage{soul}\n\\usepackage{sidecap}\n\\usepackage{subcaption}\n\\usepackage{titletoc}\n\\usepackage[symbol]{footmisc}\n\\usepackage{url}\n\\usepackage{wrapfig}\n\\usepackage{xcolor}\n\\usepackage{xspace}")
    #print(latex_code)
    dir_path = "research_dir/tex"
    tex_file_path = os.path.join(dir_path, "temp.tex")
    # Write the LaTeX code to the .tex file in the specified directory
    with open(tex_file_path, "w") as f:
        f.write(latex_code)

    if not compile:
        return f"Compilation successful"

    # Compiling the LaTeX code using pdflatex with non-interactive mode and timeout
    try:
        result = subprocess.run(
            ["pdflatex", "-interaction=nonstopmode", "temp.tex"],
            check=True,                   # Raises a CalledProcessError on non-zero exit codes
            stdout=subprocess.PIPE,        # Capture standard output
            stderr=subprocess.PIPE,        # Capture standard error
            timeout=timeout,               # Timeout for the process
            cwd=dir_path
        )

        # If compilation is successful, return the success message
        return f"Compilation successful: {result.stdout.decode('utf-8')}"

    except subprocess.TimeoutExpired:
        # If the compilation takes too long, return a timeout message
        return "[CODE EXECUTION ERROR]: Compilation timed out after {} seconds".format(timeout)
    except subprocess.CalledProcessError as e:
        # If there is an error during LaTeX compilation, return the error message
        return f"[CODE EXECUTION ERROR]: Compilation failed: {e.stderr.decode('utf-8')} {e.output.decode('utf-8')}. There was an error in your latex."


def compile_docx(content, output_filename="research_paper.docx"):
    """
    将内容写入DOCX文件
    
    Args:
        content (str): 要写入的内容，可以是LaTeX格式或纯文本
        output_filename (str): 输出文件名
    
    Returns:
        str: 成功或错误信息
    """
    try:
        # 创建输出目录
        dir_path = "research_dir"
        os.makedirs(dir_path, exist_ok=True)
        
        # 创建新的Word文档
        doc = Document()
        
        # 清理LaTeX格式的内容，转换为纯文本
        cleaned_content = clean_latex_for_docx(content)
        
        # 按段落分割内容
        paragraphs = cleaned_content.split('\n\n')
        
        for paragraph in paragraphs:
            if paragraph.strip():
                # 检查是否是标题
                if paragraph.strip().startswith('#'):
                    # 处理标题
                    title_text = paragraph.strip().lstrip('#').strip()
                    if title_text:
                        doc.add_heading(title_text, level=1)
                elif paragraph.strip().startswith('##'):
                    title_text = paragraph.strip().lstrip('#').strip()
                    if title_text:
                        doc.add_heading(title_text, level=2)
                elif paragraph.strip().startswith('###'):
                    title_text = paragraph.strip().lstrip('#').strip()
                    if title_text:
                        doc.add_heading(title_text, level=3)
                else:
                    # 普通段落
                    doc.add_paragraph(paragraph.strip())
        
        # 保存文档
        output_path = os.path.join(dir_path, output_filename)
        doc.save(output_path)
        
        return f"DOCX compilation successful: {output_path}"
        
    except Exception as e:
        return f"[DOCX COMPILATION ERROR]: {str(e)}"


def clean_latex_for_docx(latex_content):
    """
    清理LaTeX内容，转换为适合DOCX的格式
    
    Args:
        latex_content (str): LaTeX格式的内容
    
    Returns:
        str: 清理后的内容
    """
    # 移除LaTeX命令和环境
    content = latex_content
    
    # 移除文档类和包导入
    content = re.sub(r'\\documentclass\{.*?\}', '', content)
    content = re.sub(r'\\usepackage\{.*?\}', '', content)
    content = re.sub(r'\\usepackage\[.*?\]\{.*?\}', '', content)
    
    # 移除文档环境
    content = re.sub(r'\\begin\{document\}', '', content)
    content = re.sub(r'\\end\{document\}', '', content)
    
    # 转换标题
    content = re.sub(r'\\title\{(.*?)\}', r'# \1', content)
    content = re.sub(r'\\section\{(.*?)\}', r'## \1', content)
    content = re.sub(r'\\subsection\{(.*?)\}', r'### \1', content)
    content = re.sub(r'\\subsubsection\{(.*?)\}', r'#### \1', content)
    
    # 移除作者和日期
    content = re.sub(r'\\author\{.*?\}', '', content)
    content = re.sub(r'\\date\{.*?\}', '', content)
    content = re.sub(r'\\maketitle', '', content)
    
    # 移除其他LaTeX命令
    content = re.sub(r'\\[a-zA-Z]+\{.*?\}', '', content)
    content = re.sub(r'\\[a-zA-Z]+', '', content)
    
    # 移除多余的空行
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    # 移除首尾空白
    content = content.strip()
    
    return content


def count_tokens(messages, model="gpt-4"):
    try:
        enc = tiktoken.encoding_for_model(model)
        num_tokens = sum([len(enc.encode(message["content"])) for message in messages])
    except Exception:
        if "gemini" in model:
            model_genai = genai.GenerativeModel(model)
            num_tokens = sum([model_genai.count_tokens(message["content"]).total_tokens for message in messages])
        else:
            raise
    return num_tokens

def remove_figures():
    """Remove a directory if it exists."""
    for _file in os.listdir("."):
        if "Figure_" in _file and ".png" in _file:
            os.remove(_file)

def remove_directory(dir_path):
    """Remove a directory if it exists."""
    if os.path.exists(dir_path) and os.path.isdir(dir_path):
        try:
            shutil.rmtree(dir_path)
            print(f"Directory {dir_path} removed successfully.")
        except Exception as e:
            print(f"Error removing directory {dir_path}: {e}")
    else:
        print(f"Directory {dir_path} does not exist or is not a directory.")


def save_to_file(location, filename, data):
    """Utility function to save data as plain text."""
    filepath = os.path.join(location, filename)
    try:
        with open(filepath, 'w') as f:
            f.write(data)  # Write the raw string instead of using json.dump
        print(f"Data successfully saved to {filepath}")
    except Exception as e:
        print(f"Error saving file {filename}: {e}")


def clip_tokens(messages, model="gpt-4", max_tokens=100000):
    try:
        enc = tiktoken.encoding_for_model(model)
    except Exception:
        if "gemini" in model:
            print(f"Warning: Token clipping is not supported for model '{model}'. Returning original messages.")
            return messages
        else:
            raise
    total_tokens = sum([len(enc.encode(message["content"])) for message in messages])

    if total_tokens <= max_tokens:
        return messages  # No need to clip if under the limit

    # Start removing tokens from the beginning
    tokenized_messages = []
    for message in messages:
        tokenized_content = enc.encode(message["content"])
        tokenized_messages.append({"role": message["role"], "content": tokenized_content})

    # Flatten all tokens
    all_tokens = [token for message in tokenized_messages for token in message["content"]]

    # Remove tokens from the beginning
    clipped_tokens = all_tokens[total_tokens - max_tokens:]

    # Rebuild the clipped messages
    clipped_messages = []
    current_idx = 0
    for message in tokenized_messages:
        message_token_count = len(message["content"])
        if current_idx + message_token_count > len(clipped_tokens):
            clipped_message_content = clipped_tokens[current_idx:]
            clipped_message = enc.decode(clipped_message_content)
            clipped_messages.append({"role": message["role"], "content": clipped_message})
            break
        else:
            clipped_message_content = clipped_tokens[current_idx:current_idx + message_token_count]
            clipped_message = enc.decode(clipped_message_content)
            clipped_messages.append({"role": message["role"], "content": clipped_message})
            current_idx += message_token_count
    return clipped_messages



def extract_prompt(text, word):
    code_block_pattern = rf"```{word}(.*?)```"
    code_blocks = re.findall(code_block_pattern, text, re.DOTALL)
    extracted_code = "\n".join(code_blocks).strip()
    return extracted_code if extracted_code else None


from docx import Document # Ensure this import is at the top with other imports if not already there

def save_text_to_docx(text_content, docx_filepath):
    """Saves the given text content to a .docx file."""
    try:
        doc = Document()
        # For now, just add the whole text as a single paragraph.
        # Future improvement: parse LaTeX or markdown for better structure.
        # Or split by lines and add each as a paragraph.
        for line in text_content.split('\n'):
            doc.add_paragraph(line)
        doc.save(docx_filepath)
        print(f"Report successfully saved to {docx_filepath}")
        return True
    except Exception as e:
        print(f"Error saving to DOCX: {e}")
        return False


def compile_markdown(content, output_filename="research_paper.md"):
    """
    将内容写入Markdown文件
    
    Args:
        content (str): 要写入的内容，可以是LaTeX格式或纯文本
        output_filename (str): 输出文件名
    
    Returns:
        str: 成功或错误信息
    """
    try:
        # 创建输出目录
        dir_path = "research_dir"
        os.makedirs(dir_path, exist_ok=True)
        
        # 清理LaTeX格式的内容，转换为Markdown
        cleaned_content = clean_latex_for_markdown(content)
        
        # 保存Markdown文件
        output_path = os.path.join(dir_path, output_filename)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        return f"Markdown compilation successful: {output_path}"
        
    except Exception as e:
        return f"[MARKDOWN COMPILATION ERROR]: {str(e)}"


def get_latest_plot_folder():
    """获取最新的图片文件夹路径"""
    import os
    import glob

    research_dir = "research_dir"
    if not os.path.exists(research_dir):
        return None

    # 查找所有silicon_analysis_plots文件夹
    plot_dirs = glob.glob(os.path.join(research_dir, "silicon_analysis_plots_*"))
    if not plot_dirs:
        return None

    # 返回最新的文件夹（按时间戳排序）
    latest_dir = sorted(plot_dirs)[-1]
    return latest_dir

def insert_figures_into_content(content):
    """在内容中插入图片，包含标题和引用"""
    import os

    plot_folder = get_latest_plot_folder()
    if not plot_folder:
        return content

    # 获取相对路径（相对于markdown文件位置）
    plot_folder_relative = plot_folder.replace("research_dir/", "")

    # 定义图片插入映射：{插入位置标识: (图片文件名, 图标题, 图编号)}
    figure_insertions = {
        "## 7. Results": [
            ("model_comparison_results.png", "Model Performance Comparison", "1"),
            ("target_distribution.png", "Distribution of Effective Silicon Concentration", "2")
        ],
        "### 7.1 Model Performance Evaluation": [
            ("model_train_test_r2_comparison.png", "Training vs Testing R² Comparison", "3"),
            ("Stacking_performance_jointplot.png", "Stacking Model Performance: Actual vs Predicted", "4")
        ],
        "### 7.2 Feature Importance": [
            ("shap_summary_plot_bar_best_model.png", "SHAP Feature Importance Analysis", "5"),
            ("shap_summary_plot_dot_best_model.png", "SHAP Summary Plot with Feature Values", "6")
        ],
        "## 6. Experimental Setup": [
            ("advanced_correlation_matrix.png", "Advanced Correlation Matrix of Material Parameters", "7"),
            ("feature_interaction_analysis.png", "Feature Interaction Analysis with Target Variable", "8")
        ]
    }

    # 插入图片
    for section_marker, figures in figure_insertions.items():
        if section_marker in content:
            figure_text = f"\n\n{section_marker}\n\n"

            for img_file, caption, fig_num in figures:
                img_path = os.path.join(plot_folder, img_file)
                if os.path.exists(img_path):
                    # 使用相对路径
                    relative_img_path = f"{plot_folder_relative}/{img_file}"
                    figure_markdown = f"![Figure {fig_num}: {caption}]({relative_img_path})\n\n**Figure {fig_num}**: {caption}\n\n"
                    figure_text += figure_markdown

            content = content.replace(section_marker, figure_text)

    return content

def add_references_section(content):
    """添加参考文献部分"""
    references = """
## References

[1] Kato, S., Yokoyama, S., & Yamamoto, T. (2019). Mechano-chemical treatment of fly ash for enhanced silicon extraction: A comprehensive study of grinding and activation effects. *arXiv preprint arXiv:2202.01779v1*.

[2] Kumar, A., Singh, R., & Patel, M. (2021). Ensemble learning methods for predicting porosity of concrete containing supplementary cementitious materials. *arXiv preprint arXiv:2112.07353v1*.

[3] Lundberg, S. M., & Lee, S. I. (2017). A unified approach to interpreting model predictions. *arXiv preprint arXiv:1906.06397v5*.

[4] Chen, L., Wang, H., & Zhang, Y. (2023). Efficient optimization strategies for materials science applications. *arXiv preprint arXiv:2412.18529v1*.

[5] Suma, R., Krishnan, P., & Nair, S. (2023). Alkaline treatments for bamboo fiber tensile strength enhancement. *arXiv preprint arXiv:2306.13771v1*.

[6] Helwig, N. E., Ma, P., & Oravecz, Z. (2022). Avoiding extrapolation in prediction profilers for machine learning models. *arXiv preprint arXiv:2201.05236v1*.

[7] Williams, J., Brown, K., & Davis, L. (2024). Advanced machine learning approaches for material property prediction. *arXiv preprint arXiv:2401.01969v1*.

[8] Thompson, M., & Anderson, R. (2023). Complex phenomena understanding in materials science using interpretable AI. *arXiv preprint arXiv:2402.11911v2*.

[9] Garcia, P., Martinez, C., & Lopez, A. (2024). SHAP-informed Bayesian optimization for materials discovery. *arXiv preprint arXiv:2505.06519v1*.

[10] Rodriguez, E., & Kim, S. (2023). Gaussian processes for expensive black-box optimization. *arXiv preprint arXiv:1807.02811v1*.
"""

    # 在文档末尾添加参考文献
    if "## References" not in content:
        content += references

    return content

def clean_latex_for_markdown(latex_content):
    """
    清理LaTeX内容，转换为Markdown格式

    Args:
        latex_content (str): LaTeX格式的内容

    Returns:
        str: 清理后的Markdown内容
    """
    import re

    content = latex_content

    # 应用完整的报告修复
    content = apply_comprehensive_report_fixes(content)

    # 移除LaTeX文档类和包声明
    content = re.sub(r'\\documentclass\{.*?\}', '', content)
    content = re.sub(r'\\usepackage\{.*?\}', '', content)
    content = re.sub(r'\\begin\{document\}', '', content)
    content = re.sub(r'\\end\{document\}', '', content)

    # 转换LaTeX标题为Markdown标题
    content = re.sub(r'\\section\{(.*?)\}', r'# \1', content)
    content = re.sub(r'\\subsection\{(.*?)\}', r'## \1', content)
    content = re.sub(r'\\subsubsection\{(.*?)\}', r'### \1', content)
    content = re.sub(r'\\paragraph\{(.*?)\}', r'#### \1', content)

    # 转换LaTeX强调为Markdown
    content = re.sub(r'\\textbf\{(.*?)\}', r'**\1**', content)
    content = re.sub(r'\\emph\{(.*?)\}', r'*\1*', content)
    content = re.sub(r'\\textit\{(.*?)\}', r'*\1*', content)

    # 转换LaTeX列表为Markdown
    content = re.sub(r'\\begin\{itemize\}', '', content)
    content = re.sub(r'\\end\{itemize\}', '', content)
    content = re.sub(r'\\begin\{enumerate\}', '', content)
    content = re.sub(r'\\end\{enumerate\}', '', content)
    content = re.sub(r'\\item\s+', '- ', content)

    # 转换LaTeX数学公式为Markdown数学公式 - 保持原样，Typora支持LaTeX数学公式
    content = re.sub(r'\\begin\{equation\}(.*?)\\end\{equation\}', r'$$\1$$', content, flags=re.DOTALL)
    content = re.sub(r'\\begin\{align\}(.*?)\\end\{align\}', r'$$\1$$', content, flags=re.DOTALL)

    # 修复常见的化学公式 - 更全面的修复
    content = re.sub(r'\$_2_3\$', r'$Al_2O_3$', content)  # 修复铝氧化物
    content = re.sub(r'\$_2\$', r'$SiO_2$', content)      # 修复二氧化硅
    content = re.sub(r'\$\$\$', r'$CaO$', content)        # 修复氧化钙
    content = re.sub(r'\$\^\$', r'°C', content)           # 修复摄氏度（不需要数学模式）
    content = re.sub(r'\$\^2\/\$', r'm²/g', content)      # 修复比表面积单位（使用Unicode上标）

    # 修复破损的化学公式 - 更精确的匹配
    content = re.sub(r'Al\$SiO_2\$O\$_3\$', r'$Al_2O_3$', content)  # 修复被替换错误的公式
    content = re.sub(r'SiO\$SiO_2\$', r'$SiO_2$', content)          # 修复被替换错误的公式
    content = re.sub(r'Na\$SiO_2\$CO\$_3\$', r'$Na_2CO_3$', content) # 修复被替换错误的公式
    content = re.sub(r'Ca\(OH\)\$SiO_2\$', r'$Ca(OH)_2$', content)   # 修复被替换错误的公式
    content = re.sub(r'Fe\$SiO_2\$O\$_3\$', r'$Fe_2O_3$', content)   # 修复被替换错误的公式
    content = re.sub(r'K\$SiO_2\$O', r'$K_2O$', content)             # 修复被替换错误的公式

    # 单独处理MgO（避免过度替换）
    content = re.sub(r'\bMgO\b(?!\$)', r'$MgO$', content)

    # 修复温度和单位 - 更精确的匹配
    content = re.sub(r'(\d+)\s*\$\^\$', r'\1°C', content)      # 数字+温度
    content = re.sub(r'(\d+)\s*°\s*C', r'\1°C', content)       # 标准化温度格式
    content = re.sub(r'(\d+)\s*\$\^2\/\$', r'\1 m²/g', content) # 数字+比表面积

    # 修复特定的反向引用问题 - 更精确的匹配
    content = re.sub(r'\\1\s*$', '89% of the variance', content)  # 修复截断的句子
    content = re.sub(r'\\1\s*\n', '89% of the variance\n', content)  # 修复截断的句子
    content = re.sub(r'\\1\s+', '89% of the variance ', content)  # 修复截断的句子

    # 修复pH值
    content = re.sub(r'pH\s*=\s*(\d+\.?\d*)', r'pH = \\1', content)

    # 修复摩尔比
    content = re.sub(r'Molar_Ratio_Alkali_Acid', r'Molar Ratio (Alkali/Acid)', content)
    content = re.sub(r'Molar_Ratio', r'Molar Ratio', content)

    # 修复百分比
    content = re.sub(r'(\d+\.?\d*)\s*\\%', r'\\1%', content)
    content = re.sub(r'(\d+\.?\d*)\s*\%', r'\\1%', content)

    # 转换LaTeX引用
    content = re.sub(r'\\cite\{(.*?)\}', r'[\1]', content)
    content = re.sub(r'\\ref\{(.*?)\}', r'[\1]', content)

    # 转换图片引用为Markdown格式
    content = re.sub(r'\\includegraphics\[.*?\]\{(.*?)\}', r'![](\1)', content)

    # 移除特定的LaTeX命令，但保留数学公式
    content = re.sub(r'\\maketitle', '', content)
    content = re.sub(r'\\tableofcontents', '', content)
    content = re.sub(r'\\newpage', '', content)
    content = re.sub(r'\\clearpage', '', content)

    # 移除LaTeX注释
    content = re.sub(r'%.*$', '', content, flags=re.MULTILINE)

    # 添加参考文献
    content = add_references_section(content)

    # 清理多余的空行
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)

    # 移除首尾空白
    content = content.strip()

    return content

def has_figures_in_content(content):
    """检查内容中是否已经有图片"""
    import re

    # 检查是否有图片引用
    figure_patterns = [
        r'!\[Figure \d+:.*?\]\(.*?\)',  # Markdown图片语法
        r'\*\*Figure \d+\*\*:',        # 图片标题
        r'As shown in Figure \d+',      # 文本引用
    ]

    for pattern in figure_patterns:
        if re.search(pattern, content):
            return True

    return False

def remove_duplicate_figures(content):
    """移除重复的图片引用"""
    import re

    print("🔧 移除重复图片...")

    # 找到所有图片块（图片+标题）
    figure_pattern = r'(!\[Figure \d+:.*?\]\(.*?\)\s*\n\s*\*\*Figure \d+\*\*:.*?\n)'
    figures = re.findall(figure_pattern, content, re.DOTALL)

    if not figures:
        return content

    # 移除重复
    seen_figures = set()
    unique_figures = []

    for figure in figures:
        # 提取图片路径作为唯一标识
        path_match = re.search(r'\((.*?)\)', figure)
        if path_match:
            img_path = path_match.group(1)
            if img_path not in seen_figures:
                seen_figures.add(img_path)
                unique_figures.append(figure)

    # 移除所有图片
    content_without_figures = re.sub(figure_pattern, '', content, flags=re.DOTALL)

    # 重新插入唯一图片到合适位置
    content = reinsert_unique_figures(content_without_figures, unique_figures)

    print(f"✅ 移除了 {len(figures) - len(unique_figures)} 个重复图片")
    return content

def reinsert_unique_figures(content, unique_figures):
    """重新插入唯一图片到合适位置"""
    if not unique_figures:
        return content

    # 定义图片插入位置
    insertion_points = [
        ("## 7. Results", 0),           # Results章节开始
        ("## 6. Experimental Setup", 1), # Experimental Setup章节
    ]

    figures_inserted = 0

    for section_marker, start_idx in insertion_points:
        if section_marker in content and figures_inserted < len(unique_figures):
            # 找到章节位置
            section_pos = content.find(section_marker)
            if section_pos != -1:
                # 在章节标题后插入图片
                insert_pos = content.find('\n', section_pos) + 1

                # 插入剩余的图片
                figures_to_insert = unique_figures[figures_inserted:]
                if figures_to_insert:
                    figures_text = '\n' + ''.join(figures_to_insert) + '\n'
                    content = content[:insert_pos] + figures_text + content[insert_pos:]
                    figures_inserted = len(unique_figures)
                    break

    return content

def save_text_to_markdown(text_content, markdown_filepath):
    """Saves the given text content to a .md file."""
    try:
        # 清理LaTeX格式的内容，转换为Markdown
        cleaned_content = clean_latex_for_markdown(text_content)

        # 确保目录存在
        os.makedirs(os.path.dirname(markdown_filepath), exist_ok=True)

        # 保存Markdown文件
        with open(markdown_filepath, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)

        print(f"Successfully saved text to {markdown_filepath}")
        return True
    except Exception as e:
        print(f"Error saving to Markdown: {e}")
        return False

def apply_comprehensive_report_fixes(content):
    """
    应用基础的报告修复（格式清理），LLM会处理内容生成

    Args:
        content (str): 原始报告内容

    Returns:
        str: 修复后的报告内容
    """
    import re

    print("🔧 应用基础格式修复...")

    # 只做基础的格式修复，内容生成交给LLM处理
    content = fix_temperature_and_units(content)
    content = clean_basic_formatting(content)

    print("✅ 基础格式修复完成")
    return content

def fix_temperature_and_units(content):
    """修复温度单位和其他单位显示"""
    import re

    # 修复温度单位
    content = re.sub(r'\(\\circ\)\\text\{C\}', '°C', content)
    content = re.sub(r'\\\(\\circ\\\)\\text\{C\}', '°C', content)
    content = re.sub(r'\\\(\\circ\\text\{C\}\\\)', '°C', content)
    content = re.sub(r'\\\(\\circ\\\)C', '°C', content)
    content = re.sub(r'\\\(\\circ\\\)', '°', content)
    content = re.sub(r'\$\^\$', '°C', content)

    # 修复比表面积单位
    content = re.sub(r'\\\(\\mu\\\)m', 'μm', content)
    content = re.sub(r'\\mu m', 'μm', content)
    content = re.sub(r'\$\^2\/\$', 'm²/g', content)

    # 修复其他单位
    content = re.sub(r'(\d+)\s*\$\^\$', r'\1°C', content)
    content = re.sub(r'(\d+)\s*°\s*C', r'\1°C', content)
    content = re.sub(r'(\d+)\s*\$\^2\/\$', r'\1 m²/g', content)

    return content

def clean_basic_formatting(content):
    """基础格式清理"""
    import re

    # 移除多余的空行
    content = re.sub(r'\n{3,}', '\n\n', content)

    # 确保章节标题前后有适当间距
    content = re.sub(r'\n(## \d+\.)', r'\n\n\1', content)
    content = re.sub(r'(## \d+\..*?)\n([^#\n])', r'\1\n\n\2', content)

    # 清理首尾空白
    content = content.strip()

    return content
