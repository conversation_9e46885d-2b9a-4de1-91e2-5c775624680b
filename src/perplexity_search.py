"""
Perplexity API integration for literature review and research assistance.
This module provides functionality to use Perplexity AI for comprehensive literature reviews
and research question answering.
"""

import requests
import json
import time
from typing import List, Dict, Optional, Any
import os


class PerplexitySearch:
    """
    Perplexity AI search engine for literature review and research assistance.
    
    This class provides methods to search for academic papers, generate literature reviews,
    and answer research questions using Perplexity's AI-powered search capabilities.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Perplexity search engine.
        
        Args:
            api_key: Perplexity API key. If not provided, will try to get from environment.
        """
        self.api_key = api_key or os.getenv('PERPLEXITY_API_KEY')
        if not self.api_key:
            raise ValueError("Perplexity API key is required. Set PERPLEXITY_API_KEY environment variable or pass api_key parameter.")
        
        self.base_url = "https://api.perplexity.ai/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # Available models
        self.models = {
            "sonar-pro": "llama-3.1-sonar-large-128k-online",
            "sonar-small": "llama-3.1-sonar-small-128k-online", 
            "sonar-huge": "llama-3.1-sonar-huge-128k-online"
        }
        
        self.default_model = "sonar-pro"
        self.max_retries = 3
        self.retry_delay = 2.0
    
    def _make_request(self, messages: List[Dict], model: str = None, temperature: float = 0.2) -> Dict[str, Any]:
        """
        Make a request to the Perplexity API.
        
        Args:
            messages: List of message dictionaries
            model: Model to use (defaults to sonar-pro)
            temperature: Temperature for response generation
            
        Returns:
            API response dictionary
        """
        if model is None:
            model = self.default_model
        
        model_name = self.models.get(model, model)
        
        payload = {
            "model": model_name,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": 4000,
            "return_citations": True,
            "search_domain_filter": ["pubmed.ncbi.nlm.nih.gov", "arxiv.org", "scholar.google.com"],
            "return_images": False
        }
        
        for attempt in range(self.max_retries):
            try:
                response = requests.post(
                    self.base_url,
                    headers=self.headers,
                    json=payload,
                    timeout=60
                )
                response.raise_for_status()
                return response.json()
            
            except requests.exceptions.RequestException as e:
                print(f"Perplexity API request failed (attempt {attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (attempt + 1))
                else:
                    raise Exception(f"Perplexity API request failed after {self.max_retries} attempts: {e}")
    
    def search_literature(self, query: str, focus_area: str = None, num_papers: int = 10) -> str:
        """
        Search for academic literature using Perplexity AI.
        
        Args:
            query: Research query or topic
            focus_area: Specific focus area (e.g., "machine learning", "materials science")
            num_papers: Number of papers to find (approximate)
            
        Returns:
            Formatted string containing literature search results
        """
        # Construct search prompt
        search_prompt = f"""
        Please conduct a comprehensive literature search on the following research topic: "{query}"
        
        Requirements:
        1. Find approximately {num_papers} relevant academic papers
        2. Focus on recent publications (last 5 years preferred)
        3. Include papers from reputable journals and conferences
        4. Provide for each paper:
           - Title
           - Authors
           - Publication year
           - Journal/Conference
           - Brief summary of key findings
           - DOI or arXiv ID if available
        
        {f"Special focus area: {focus_area}" if focus_area else ""}
        
        Format the results as a structured list with clear separation between papers.
        """
        
        messages = [
            {
                "role": "system",
                "content": "You are a research assistant specializing in academic literature search. Provide comprehensive, accurate, and well-structured literature reviews with proper citations."
            },
            {
                "role": "user", 
                "content": search_prompt
            }
        ]
        
        try:
            response = self._make_request(messages)
            content = response['choices'][0]['message']['content']
            
            # Add citations if available
            if 'citations' in response and response['citations']:
                content += "\n\n=== SOURCES ===\n"
                for i, citation in enumerate(response['citations'], 1):
                    content += f"[{i}] {citation}\n"
            
            return content
            
        except Exception as e:
            return f"Error conducting literature search: {str(e)}"
    
    def generate_literature_review(self, topic: str, papers_context: str = None) -> str:
        """
        Generate a comprehensive literature review on a given topic.
        
        Args:
            topic: Research topic for the literature review
            papers_context: Additional context about specific papers to include
            
        Returns:
            Formatted literature review text
        """
        review_prompt = f"""
        Please write a comprehensive literature review on the topic: "{topic}"
        
        The literature review should include:
        1. Introduction to the research area
        2. Current state of research
        3. Key methodologies and approaches
        4. Recent developments and trends
        5. Research gaps and future directions
        6. Proper academic citations
        
        {f"Additional context to consider: {papers_context}" if papers_context else ""}
        
        Write in academic style suitable for a research paper.
        """
        
        messages = [
            {
                "role": "system",
                "content": "You are an expert academic writer specializing in literature reviews. Write comprehensive, well-structured reviews with proper academic tone and citations."
            },
            {
                "role": "user",
                "content": review_prompt
            }
        ]
        
        try:
            response = self._make_request(messages, temperature=0.3)
            content = response['choices'][0]['message']['content']
            
            # Add sources section
            if 'citations' in response and response['citations']:
                content += "\n\n## References\n"
                for i, citation in enumerate(response['citations'], 1):
                    content += f"{i}. {citation}\n"
            
            return content
            
        except Exception as e:
            return f"Error generating literature review: {str(e)}"
    
    def answer_research_question(self, question: str, context: str = None) -> str:
        """
        Answer a specific research question using Perplexity's knowledge base.
        
        Args:
            question: Research question to answer
            context: Additional context for the question
            
        Returns:
            Detailed answer with citations
        """
        qa_prompt = f"""
        Research Question: {question}
        
        {f"Context: {context}" if context else ""}
        
        Please provide a comprehensive, evidence-based answer to this research question. 
        Include relevant citations and references to support your response.
        """
        
        messages = [
            {
                "role": "system", 
                "content": "You are a research expert. Provide detailed, accurate, and well-cited answers to research questions."
            },
            {
                "role": "user",
                "content": qa_prompt
            }
        ]
        
        try:
            response = self._make_request(messages)
            content = response['choices'][0]['message']['content']
            
            if 'citations' in response and response['citations']:
                content += "\n\n=== REFERENCES ===\n"
                for i, citation in enumerate(response['citations'], 1):
                    content += f"[{i}] {citation}\n"
            
            return content
            
        except Exception as e:
            return f"Error answering research question: {str(e)}"
    
    def find_papers_by_str(self, query: str, N: int = 10) -> str:
        """
        Find papers by search string - compatible with ArxivSearch interface.

        Args:
            query: Search query string
            N: Number of papers to return

        Returns:
            Formatted string with paper information
        """
        return self.search_literature(query, num_papers=N)

    def comprehensive_literature_analysis(self, topic: str, focus_areas: List[str] = None) -> Dict[str, str]:
        """
        Perform comprehensive literature analysis for Agent Laboratory.

        Args:
            topic: Main research topic
            focus_areas: List of specific focus areas to explore

        Returns:
            Dictionary containing different aspects of literature analysis
        """
        results = {}

        try:
            # Main literature review
            print(f"🔍 Generating main literature review for: {topic}")
            results['main_review'] = self.generate_literature_review(topic)

            # Recent developments
            recent_query = f"recent developments in {topic} 2023 2024"
            print(f"📅 Searching for recent developments...")
            results['recent_developments'] = self.search_literature(recent_query, num_papers=5)

            # Methodological approaches
            methods_query = f"methods approaches techniques {topic}"
            print(f"🔬 Analyzing methodological approaches...")
            results['methodologies'] = self.search_literature(methods_query, num_papers=5)

            # Research gaps and future directions
            gaps_query = f"research gaps future directions challenges {topic}"
            print(f"🎯 Identifying research gaps...")
            results['research_gaps'] = self.answer_research_question(
                f"What are the main research gaps and future directions in {topic}?"
            )

            # Focus area analysis
            if focus_areas:
                results['focus_areas'] = {}
                for area in focus_areas:
                    print(f"🎯 Analyzing focus area: {area}")
                    results['focus_areas'][area] = self.search_literature(
                        f"{topic} {area}", num_papers=3
                    )

            return results

        except Exception as e:
            return {"error": f"Error in comprehensive analysis: {str(e)}"}

    def format_comprehensive_review(self, analysis_results: Dict[str, str], topic: str) -> str:
        """
        Format comprehensive literature analysis results into a structured review.

        Args:
            analysis_results: Results from comprehensive_literature_analysis
            topic: Research topic

        Returns:
            Formatted literature review string
        """
        if "error" in analysis_results:
            return analysis_results["error"]

        review = f"# Comprehensive Literature Review: {topic}\n\n"

        if "main_review" in analysis_results:
            review += "## Overview\n"
            review += analysis_results["main_review"] + "\n\n"

        if "recent_developments" in analysis_results:
            review += "## Recent Developments\n"
            review += analysis_results["recent_developments"] + "\n\n"

        if "methodologies" in analysis_results:
            review += "## Methodological Approaches\n"
            review += analysis_results["methodologies"] + "\n\n"

        if "research_gaps" in analysis_results:
            review += "## Research Gaps and Future Directions\n"
            review += analysis_results["research_gaps"] + "\n\n"

        if "focus_areas" in analysis_results:
            review += "## Specialized Focus Areas\n"
            for area, content in analysis_results["focus_areas"].items():
                review += f"### {area}\n"
                review += content + "\n\n"

        return review
