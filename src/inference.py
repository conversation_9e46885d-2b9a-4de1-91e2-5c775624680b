import time, tiktoken
from openai import OpenAI
import openai
import os, anthropic, json
import google.generativeai as genai
import requests

TOKENS_IN = dict()
TOKENS_OUT = dict()

encoding = tiktoken.get_encoding("cl100k_base")

def curr_cost_est():
    costmap_in = {
        "gpt-4o": 2.50 / 1000000,
        "gpt-4o-mini": 0.150 / 1000000,
        "o1-preview": 15.00 / 1000000,
        "o1-mini": 3.00 / 1000000,
        "claude-3-5-sonnet": 3.00 / 1000000,
        "gemini-2.5-flash-preview-05-20": 0.35 / 1000000,
        "gemini-2.5-flash": 0.35 / 1000000,
        "perplexity-sonar-pro": 1.00 / 1000000,
        "perplexity-sonar-small": 0.20 / 1000000,
        "perplexity-sonar-huge": 5.00 / 1000000,
        "openai-compatible": 0.150 / 1000000,  # Added cost for openai-compatible
    }
    costmap_out = {
        "gpt-4o": 10.00/ 1000000,
        "gpt-4o-mini": 0.6 / 1000000,
        "o1-preview": 60.00 / 1000000,
        "o1-mini": 12.00 / 1000000,
        "claude-3-5-sonnet": 12.00 / 1000000,
        "gemini-2.5-flash-preview-05-20": 0.70 / 1000000,
        "gemini-2.5-flash": 0.70 / 1000000,
        "perplexity-sonar-pro": 1.00 / 1000000,
        "perplexity-sonar-small": 0.20 / 1000000,
        "perplexity-sonar-huge": 5.00 / 1000000,
        "openai-compatible": 0.6 / 1000000,  # Added cost for openai-compatible
    }
    return sum([costmap_in[_]*TOKENS_IN[_] for _ in TOKENS_IN]) + sum([costmap_out[_]*TOKENS_OUT[_] for _ in TOKENS_OUT])

def query_model(model_str, prompt, system_prompt, openai_api_key=None, anthropic_api_key=None, perplexity_api_key=None, openai_base_url=None, openai_model_name=None, tries=5, timeout=5.0, temp=None, print_cost=True, version="1.5"):
    preloaded_api = os.getenv('OPENAI_API_KEY')
    if openai_api_key is None and preloaded_api is not None:
        openai_api_key = preloaded_api
    if openai_api_key is None and anthropic_api_key is None:
        raise Exception("No API key provided in query_model function")
    if openai_api_key is not None:
        openai.api_key = openai_api_key
        os.environ["OPENAI_API_KEY"] = openai_api_key
    if anthropic_api_key is not None:
        os.environ["ANTHROPIC_API_KEY"] = anthropic_api_key
    for _ in range(tries):
        try:
            if model_str == "gpt-4o-mini" or model_str == "gpt4omini" or model_str == "gpt-4omini" or model_str == "gpt4o-mini":
                model_str = "gpt-4o-mini"
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}]
                if version == "0.28":
                    if temp is None:
                        completion = openai.ChatCompletion.create(
                            model=f"{model_str}",  # engine = "deployment_name".
                            messages=messages
                        )
                    else:
                        completion = openai.ChatCompletion.create(
                            model=f"{model_str}",  # engine = "deployment_name".
                            messages=messages, temperature=temp
                        )
                else:
                    client = OpenAI()
                    if temp is None:
                        completion = client.chat.completions.create(
                            model="gpt-4o-mini-2024-07-18", messages=messages, )
                    else:
                        completion = client.chat.completions.create(
                            model="gpt-4o-mini-2024-07-18", messages=messages, temperature=temp)
                answer = completion.choices[0].message.content
            elif "gemini" in model_str.lower():
                # Use the google-generativeai library for all Gemini models
                if "GOOGLE_API_KEY" not in os.environ:
                    if openai_api_key: # Fallback to openai_api_key if it holds the Google key
                        os.environ["GOOGLE_API_KEY"] = openai_api_key
                    else:
                        raise Exception("GOOGLE_API_KEY environment variable not set and no fallback key provided.")
                
                # 设置环境变量以解决TLS/SSL连接问题
                os.environ['GRPC_SSL_CIPHER_SUITES'] = 'HIGH+ECDSA'
                os.environ['GRPC_EXPERIMENTAL_DISABLE_GRPCLB'] = '1'
                
                genai.configure(api_key=os.environ["GOOGLE_API_KEY"])
                
                model = genai.GenerativeModel(model_name=model_str) # system_instruction removed
                
                # Combine system_prompt and prompt if system_prompt exists
                if system_prompt and system_prompt.strip():
                    combined_prompt = f"{system_prompt}\n\n{prompt}"
                else:
                    combined_prompt = prompt
                response = model.generate_content(combined_prompt)
                answer = response.text

                try:
                    # Use the model's token counter for accuracy
                    input_tokens = model.count_tokens(prompt).total_tokens
                    output_tokens = model.count_tokens(answer).total_tokens
                    if model_str not in TOKENS_IN:
                        TOKENS_IN[model_str] = 0
                        TOKENS_OUT[model_str] = 0
                    TOKENS_IN[model_str] += input_tokens
                    TOKENS_OUT[model_str] += output_tokens
                except Exception as e:
                    print(f"Warning: Could not get token usage for {model_str}: {e}")
            elif model_str == "claude-3.5-sonnet":
                client = anthropic.Anthropic(api_key=os.environ["ANTHROPIC_API_KEY"])
                message = client.messages.create(
                    model="claude-3-5-sonnet-latest",
                    system=system_prompt,
                    messages=[{"role": "user", "content": prompt}])
                answer = json.loads(message.to_json())["content"][0]["text"]
            elif model_str == "gpt4o" or model_str == "gpt-4o":
                model_str = "gpt-4o"
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}]
                if version == "0.28":
                    if temp is None:
                        completion = openai.ChatCompletion.create(
                            model=f"{model_str}",  # engine = "deployment_name".
                            messages=messages
                        )
                    else:
                        completion = openai.ChatCompletion.create(
                            model=f"{model_str}",  # engine = "deployment_name".
                            messages=messages, temperature=temp
                        )

                else:
                    client = OpenAI()
                    if temp is None:
                        completion = client.chat.completions.create(
                            model="gpt-4o-2024-08-06", messages=messages, )
                    else:
                        completion = client.chat.completions.create(
                            model="gpt-4o-2024-08-06", messages=messages, temperature=temp)
                answer = completion.choices[0].message.content
            elif model_str == "o1-mini":
                model_str = "o1-mini"
                messages = [
                    {"role": "user", "content": system_prompt + prompt}]
                if version == "0.28":
                    completion = openai.ChatCompletion.create(
                        model=f"{model_str}",  # engine = "deployment_name".
                        messages=messages
                    )
                else:
                    client = OpenAI()
                    completion = client.chat.completions.create(
                        model="o1-mini-2024-09-12", messages=messages)
                answer = completion.choices[0].message.content
            elif model_str == "o1-preview":
                model_str = "o1-preview"
                messages = [
                    {"role": "user", "content": system_prompt + prompt}]
                if version == "0.28":
                    completion = openai.ChatCompletion.create(
                        model=f"{model_str}",  # engine = "deployment_name".
                        messages=messages
                    )
                else:
                    client = OpenAI()
                    completion = client.chat.completions.create(
                        model="o1-preview", messages=messages)
                answer = completion.choices[0].message.content
            elif model_str == "openai-compatible":
                if openai_api_key is None:
                    raise Exception("openai_api_key must be provided for openai-compatible model.")
                if openai_base_url is None:
                    raise Exception("openai_base_url must be provided for openai-compatible model.")

                client = OpenAI(api_key=openai_api_key, base_url=openai_base_url)
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ]
                if temp is None:
                    completion = client.chat.completions.create(
                        model=openai_model_name, messages=messages
                    )
                else:
                    completion = client.chat.completions.create(
                        model=openai_model_name, messages=messages, temperature=temp
                    )
                answer = completion.choices[0].message.content
            elif "perplexity" in model_str.lower():
                # Handle Perplexity models
                if perplexity_api_key is None:
                    perplexity_api_key = os.getenv('PERPLEXITY_API_KEY')
                if perplexity_api_key is None:
                    raise Exception("PERPLEXITY_API_KEY environment variable not set and no API key provided.")

                # Map model names
                model_mapping = {
                    "perplexity-sonar-pro": "llama-3.1-sonar-large-128k-online",
                    "perplexity-sonar-small": "llama-3.1-sonar-small-128k-online",
                    "perplexity-sonar-huge": "llama-3.1-sonar-huge-128k-online"
                }

                actual_model = model_mapping.get(model_str, "llama-3.1-sonar-large-128k-online")

                headers = {
                    "Authorization": f"Bearer {perplexity_api_key}",
                    "Content-Type": "application/json"
                }

                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ]

                payload = {
                    "model": actual_model,
                    "messages": messages,
                    "temperature": temp if temp is not None else 0.2,
                    "max_tokens": 4000,
                    "return_citations": True,
                    "search_domain_filter": ["pubmed.ncbi.nlm.nih.gov", "arxiv.org", "scholar.google.com"]
                }

                response = requests.post(
                    "https://api.perplexity.ai/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=60
                )
                response.raise_for_status()
                result = response.json()
                answer = result['choices'][0]['message']['content']

                # Add citations if available
                if 'citations' in result and result['citations']:
                    answer += "\n\n=== SOURCES ===\n"
                    for i, citation in enumerate(result['citations'], 1):
                        answer += f"[{i}] {citation}\n"

            if "gemini" not in model_str.lower() and "perplexity" not in model_str.lower() and model_str != "openai-compatible":
                if model_str in ["o1-preview", "o1-mini", "claude-3.5-sonnet"]:
                    encoding = tiktoken.encoding_for_model("gpt-4o")
                else:
                    try:
                        encoding = tiktoken.encoding_for_model(model_str)
                    except KeyError:
                        if "gemini" in model_str.lower():
                            print(f"Warning: Could not map '{model_str}' to a tokenizer. Defaulting to 'cl100k_base' for Gemini model.")
                            encoding = tiktoken.get_encoding("cl100k_base")
                        else:
                            raise
                if model_str not in TOKENS_IN:
                    TOKENS_IN[model_str] = 0
                    TOKENS_OUT[model_str] = 0
                TOKENS_IN[model_str] += len(encoding.encode(system_prompt + prompt))
                TOKENS_OUT[model_str] += len(encoding.encode(answer))
            elif "perplexity" in model_str.lower() or model_str == "openai-compatible":
                # For Perplexity and openai-compatible models, use approximate token counting
                encoding = tiktoken.get_encoding("cl100k_base")
                if model_str not in TOKENS_IN:
                    TOKENS_IN[model_str] = 0
                    TOKENS_OUT[model_str] = 0
                TOKENS_IN[model_str] += len(encoding.encode(system_prompt + prompt))
                TOKENS_OUT[model_str] += len(encoding.encode(answer))
            if print_cost:
                print(f"Current experiment cost = ${curr_cost_est()}, ** Approximate values, may not reflect true cost")
            return answer
        except Exception as e:
            print(f"Inference Exception Type: {type(e)}")
            print(f"Inference Exception Args: {e.args}")
            print(f"Inference Exception Details: {repr(e)}")
            time.sleep(timeout)
            continue
    raise Exception("Max retries: timeout")


#print(query_model(model_str="o1-mini", prompt="hi", system_prompt="hey"))