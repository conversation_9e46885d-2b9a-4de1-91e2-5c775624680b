

import os
import datetime
import argparse
from analysis_utils.data_processing import load_data, feature_engineering, preprocess_data
from analysis_utils.eda import perform_eda
from analysis_utils.modeling import tune_and_evaluate_models
from analysis_utils.summary import save_full_analysis_summary

def run_full_analysis(data_path: str, target_column: str = "Effective_Silicon", output_base_dir: str = "outputs/silicon_analysis_plots"):
    """
    Orchestrates the full analysis pipeline by calling modularized functions.
    """
    print(f"Starting full analysis for data: {data_path}")
    
    # Create a unique output folder for this analysis run
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_folder = os.path.join(output_base_dir, f"silicon_analysis_plots_{timestamp}")
    os.makedirs(output_folder, exist_ok=True)
    print(f"Output will be saved to: {output_folder}")

    # 1. Data Loading and Preprocessing
    df = load_data(data_path)
    if df is None:
        print(f"Analysis stopped because data could not be loaded from {data_path}.")
        return

    df, feature_definitions = feature_engineering(df)
    X, y, preprocessor = preprocess_data(df, target_column)

    # 2. Exploratory Data Analysis (EDA)
    eda_plot_paths = perform_eda(df, target_column, output_folder)

    # 3. Model Tuning, Evaluation, and Interpretation
    trained_models, results_df, plot_paths = tune_and_evaluate_models(X, y, preprocessor, output_folder)
    all_plot_paths = {**eda_plot_paths, **plot_paths}

    # Print model evaluation summary to console
    print("\n--- Model Evaluation Summary (Console Output) ---")
    print(results_df.to_string())

    # 4. Save Final Summary
    save_full_analysis_summary(results_df, output_folder)

    # 5. Print all plot paths for the main workflow to capture
    print("\n--- Generated Plot Paths (Console Output) ---")
    for key, path in all_plot_paths.items():
        print(f"{key}: {path}")

    print("\n---PLOT_PATHS_START---")
    for key, path in all_plot_paths.items():
        print(f"{key}:{path}")
    print("---PLOT_PATHS_END---")

    print("\n" + "="*80)
    print("✅ Full analysis pipeline completed successfully!")
    print("="*80)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run full analysis pipeline for silicon yield prediction.")
    parser.add_argument("--data_path", type=str, default="data/Si2025_5_4.csv", help="Path to the data file.")
    parser.add_argument("--target_column", type=str, default="Effective_Silicon", help="Name of the target column.")
    args = parser.parse_args()

    run_full_analysis(data_path=args.data_path, target_column=args.target_column)
