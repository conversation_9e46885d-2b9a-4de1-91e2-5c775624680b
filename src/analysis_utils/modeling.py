import pandas as pd
import numpy as np
import os
import json
from datetime import datetime

from sklearn.model_selection import KFold, GridSearchCV
from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error
from sklearn.base import clone
from scipy.stats import ttest_rel
from sklearn.ensemble import StackingRegressor
from sklearn.linear_model import LinearRegression

# 模型导入
from sklearn.neighbors import KNeighborsRegressor
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor, GradientBoostingRegressor, HistGradientBoostingRegressor, AdaBoostRegressor
from sklearn.svm import SVR
from sklearn.tree import DecisionTreeRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.linear_model import BayesianRidge

try:
    import xgboost as xgb
    XGB_AVAILABLE = True
except ImportError:
    XGB_AVAILABLE = False
try:
    from lightgbm import LGBMRegressor
    LGBM_AVAILABLE = True
except ImportError:
    LGBM_AVAILABLE = False
try:
    from catboost import CatBoostRegressor
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

# 从本地模块导入
from .plotting import plot_model_comparison_results, plot_train_test_r2_comparison, plot_actual_vs_predicted_jointgrid
from .interpretation import explain_stacking_model_fully, create_pdp_analysis, _plot_shap_summary_plot

# 用于系统调优的更全面的参数网格
PARAM_GRIDS = {
    'KNN': (KNeighborsRegressor(), {
        'n_neighbors': list(range(1, 31)),
        'weights': ['uniform', 'distance'],
        'p': [1, 2]
    }),
    'RF': (RandomForestRegressor(random_state=42), {
        'n_estimators': [100, 200],
        'max_depth': [None, 10, 20],
        'min_samples_split': [2, 5],
        'min_samples_leaf': [1, 2],
        'bootstrap': [True, False]
    }),
    'XGB': (xgb.XGBRegressor(random_state=42, objective='reg:squarederror') if XGB_AVAILABLE else None, {
        'n_estimators': [100, 200],
        'max_depth': [3, 6, 10],
        'learning_rate': [0.01, 0.1],
        'subsample': [0.8, 1.0],
        'colsample_bytree': [0.8, 1.0],
        'gamma': [0, 0.1],
        'min_child_weight': [1, 5]
    }),
    'LGBM': (LGBMRegressor(random_state=42, verbose=-1) if LGBM_AVAILABLE else None, {
        'n_estimators': [100, 200],
        'max_depth': [3, 10],
        'learning_rate': [0.01, 0.1],
        'subsample': [0.8, 1.0],
        'colsample_bytree': [0.8, 1.0],
        'min_child_samples': [10, 20],
        'reg_alpha': [0, 0.1],
        'reg_lambda': [0, 0.1]
    }),
    'CatBoost': (CatBoostRegressor(random_state=42, verbose=0) if CATBOOST_AVAILABLE else None, {
        'iterations': [100, 200],
        'depth': [3, 10],
        'learning_rate': [0.01, 0.3],
        'l2_leaf_reg': [1, 5],
        'border_count': [32, 128],
        'subsample': [0.8, 1.0],
        'colsample_bylevel': [0.8, 1.0]
    }),
    'MLP': (MLPRegressor(random_state=42, max_iter=2000), {
        'hidden_layer_sizes': [(50,), (100,), (50, 50)],
        'activation': ['relu', 'tanh'],
        'solver': ['adam', 'lbfgs'],
        'alpha': [0.0001, 0.001, 0.01],
        'learning_rate': ['constant', 'adaptive'],
        'learning_rate_init': [0.001, 0.01, 0.1]
    })
}

def tune_and_evaluate_models(X, y, preprocessor, output_folder):
    """
    系统地调优、评估和可视化多个回归模型。
    """
    print("\n" + "="*80)
    print("开始系统模型调优和评估")
    print("="*80)

    # 我们需要先分割数据以获得一致的训练/测试集
    from sklearn.model_selection import train_test_split
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    X_train_processed = preprocessor.fit_transform(X_train)
    X_test_processed = preprocessor.transform(X_test)
    
    # Get feature names from the preprocessor
    try:
        feature_names_list = preprocessor.get_feature_names_out()
    except AttributeError:
        # Fallback for older scikit-learn versions or complex transformers
        num_features = list(X_train.select_dtypes(include=np.number).columns)
        cat_transformer = preprocessor.named_transformers_.get('cat')
        if cat_transformer and hasattr(cat_transformer, 'get_feature_names_out'):
            ohe_features = list(cat_transformer.get_feature_names_out())
        else:
            ohe_features = [f'cat_feature_{i}' for i in range(len(preprocessor.named_transformers_['cat'].categories_))]
        feature_names_list = num_features + ohe_features

    # Convert processed arrays back to DataFrames with correct feature names
    X_train_processed = pd.DataFrame(X_train_processed, columns=feature_names_list)
    X_test_processed = pd.DataFrame(X_test_processed, columns=feature_names_list)

    results_list = []
    trained_models = {}
    plot_paths = {}

    for name, (estimator, grid) in PARAM_GRIDS.items():
        if estimator is None: continue
        print(f"\n--- 正在调优模型: {name} ---")
        
        search = GridSearchCV(estimator, grid, cv=5, scoring='r2', n_jobs=-1, verbose=1)
        
        try:
            search.fit(X_train_processed, y_train)
            best_model = search.best_estimator_
            
            y_pred_test = best_model.predict(X_test_processed)
            y_pred_train = best_model.predict(X_train_processed)

            results_list.append({
                'Model': name,
                'Best Params': str(search.best_params_),
                'Test R2': r2_score(y_test, y_pred_test),
                'Train R2': r2_score(y_train, y_pred_train),
                'Test MAE': mean_absolute_error(y_test, y_pred_test),
                'Test RMSE': np.sqrt(mean_squared_error(y_test, y_pred_test))
            })
            trained_models[name] = best_model
            
            print(f"--- {name} 的结果 ---")
            print(f"最佳参数: {search.best_params_}")
            print(f"测试集 R² 分数: {results_list[-1]['Test R2']:.4f}")

            plot_path = plot_actual_vs_predicted_jointgrid(
                y_train, y_pred_train, y_test, y_pred_test,
                output_folder, model_name=name
            )
            if plot_path:
                plot_paths[f"{name}_jointplot"] = plot_path
        except Exception as e:
            print(f"错误: 无法调优或评估 {name}。原因: {e}")
            # Ensure results_list is always appended, even on error
            results_list.append({
                'Model': name, 'Best Params': '失败', 'Test R2': -999,
                'Train R2': -999, 'Test MAE': -999, 'Test RMSE': -999
            })
            trained_models[name] = None

    results_df = pd.DataFrame(results_list)
    print("\n--- 整体模型调优总结 ---")
    print(results_df[['Model', 'Test R2', 'Train R2', 'Test MAE']].to_string())
    
    summary_csv_path = os.path.join(output_folder, 'model_tuning_summary.csv')
    results_df.to_csv(summary_csv_path, index=False)
    print(f"\n调优总结已保存到: {summary_csv_path}")

    comparison_plot_path = plot_model_comparison_results(results_df, output_folder)
    if comparison_plot_path:
        plot_paths["model_comparison_plot"] = comparison_plot_path
    
    traintest_plot_path = plot_train_test_r2_comparison(results_df, output_folder)
    if traintest_plot_path:
        plot_paths["train_test_r2_comparison_plot"] = traintest_plot_path
    
    base_learners = [(name, model) for name, model in trained_models.items() if model is not None]
    
    if len(base_learners) > 1:
        print("\n--- 正在构建和评估堆叠集成模型 ---")
        meta_model = LinearRegression()
        stacking_regressor = StackingRegressor(estimators=base_learners, final_estimator=meta_model, cv=5, n_jobs=-1)
        
        try:
            stacking_regressor.fit(X_train_processed, y_train)
            y_pred_test_stack = stacking_regressor.predict(X_test_processed)
            y_pred_train_stack = stacking_regressor.predict(X_train_processed)
            
            stacking_results = {
                'Model': 'Stacking', 'Best Params': 'N/A',
                'Test R2': r2_score(y_test, y_pred_test_stack),
                'Train R2': r2_score(y_train, y_pred_train_stack),
                'Test MAE': mean_absolute_error(y_test, y_pred_test_stack),
                'Test RMSE': np.sqrt(mean_squared_error(y_test, y_pred_test_stack))
            }
            results_df = pd.concat([results_df, pd.DataFrame([stacking_results])], ignore_index=True)
            
            plot_model_comparison_results(results_df, output_folder)
            plot_train_test_r2_comparison(results_df, output_folder)
            plot_actual_vs_predicted_jointgrid(y_train, y_pred_train_stack, y_test, y_pred_test_stack, output_folder, model_name="Stacking")
            
            trained_models['Stacking'] = stacking_regressor
        except Exception as e:
            print(f"错误: 无法训练或评估堆叠模型。原因: {e}")
            trained_models['Stacking'] = None
    
    final_best_model_row = results_df.loc[results_df['Test R2'].idxmax()]
    final_best_model_name = final_best_model_row['Model']
    final_best_model_instance = trained_models.get(final_best_model_name)

    if final_best_model_instance:
        print(f"\n--- 正在对最佳整体模型进行详细分析: {final_best_model_name} ---")
        
        feature_names_list = []
        try:
            num_features = list(X_train.select_dtypes(include=np.number).columns)
            cat_transformer = preprocessor.named_transformers_.get('cat')
            if cat_transformer and hasattr(cat_transformer, 'get_feature_names_out'):
                ohe_features = list(cat_transformer.get_feature_names_out())
            else:
                 ohe_features = []
            feature_names_list = num_features + ohe_features
        except Exception:
             feature_names_list = [f"feature_{i}" for i in range(X_test_processed.shape[1])]
        
        X_test_processed_df = pd.DataFrame(X_test_processed, columns=feature_names_list)

        if final_best_model_name == 'Stacking':
            explain_stacking_model_fully(
                final_best_model_instance, X_train_processed, X_test_processed_df,
                preprocessor, output_folder, y_train, y_test
            )
        else:
            try:
                # 使用改进的 SHAP 和 PDP 分析
                from .interpretation import robust_shap_analysis, robust_pdp_analysis

                # 执行 SHAP 分析
                shap_plot_paths = robust_shap_analysis(
                    final_best_model_instance,
                    X_train_processed,
                    X_test_processed,
                    feature_names_list,
                    output_folder,
                    final_best_model_name
                )

                # 将 SHAP 图路径添加到 plot_paths 字典
                for i, path in enumerate(shap_plot_paths):
                    if 'bar' in path:
                        plot_paths[f"{final_best_model_name}_shap_bar"] = path
                    elif 'dot' in path:
                        plot_paths[f"{final_best_model_name}_shap_dot"] = path
                    elif 'dependence' in path:
                        plot_paths[f"{final_best_model_name}_shap_dep_{i}"] = path

                # 执行 PDP 分析
                pdp_path = robust_pdp_analysis(
                    final_best_model_instance,
                    X_test_processed,
                    feature_names_list,
                    output_folder,
                    final_best_model_name
                )

                if pdp_path:
                    plot_paths[f"{final_best_model_name}_pdp"] = pdp_path

            except Exception as e:
                print(f"警告: 无法为最佳模型 {final_best_model_name} 创建 SHAP/PDP 图。原因: {e}")
                import traceback
                traceback.print_exc()
        
    # 保存完整总结
    summary_json_path = os.path.join(output_folder, 'full_analysis_summary.json')
    with open(summary_json_path, 'w', encoding='utf-8') as f:
        json.dump(results_df.to_dict('records'), f, indent=4, ensure_ascii=False)
    print(f"\n✅ 完整分析总结已保存到: {summary_json_path}")
    
    return trained_models, results_df, plot_paths