import os
import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score

# Unified plotting style for consistency
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams.update({
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans'],
    'font.size': 14,
    'axes.titlesize': 16,
    'axes.labelsize': 14,
    'xtick.labelsize': 14,
    'ytick.labelsize': 14,
    'legend.fontsize': 14,
    'figure.titlesize': 18,
    'axes.unicode_minus': False,
})

CONTINUOUS_CMAP = 'viridis'

def safe_filename(name):
    return "".join([c for c in name if c.isalpha() or c.isdigit() or c.isspace()]).rstrip()

def plot_actual_vs_predicted_jointgrid(y_train, y_pred_train, y_test, y_pred_test, output_folder, model_name="Model"):
    """
    Generates and saves a JointGrid scatter plot of actual vs. predicted values.
    """
    print(f"\n--- Generating JointGrid plot for {model_name} ---")
    try:
        data_train = pd.DataFrame({'True': y_train, 'Predicted': y_pred_train, 'Data Set': 'Train'})
        data_test = pd.DataFrame({'True': y_test, 'Predicted': y_pred_test, 'Data Set': 'Test'})
        data = pd.concat([data_train, data_test], ignore_index=True)
        
        r2_train_score = r2_score(y_train, y_pred_train)
        r2_test_score = r2_score(y_test, y_pred_test)

        palette = {'Train': '#440154', 'Test': '#21908c'}
        plt.figure(figsize=(12, 10))
        g = sns.JointGrid(data=data, x="True", y="Predicted", hue="Data Set", height=10, palette=palette)
        g.plot_joint(sns.scatterplot, alpha=0.6, s=50)
        
        sns.regplot(data=data_train, x="True", y="Predicted", scatter=False, ax=g.ax_joint,
                   color=palette['Train'], label=f'Train (R² = {r2_train_score:.3f})', line_kws={'linewidth': 2})
        sns.regplot(data=data_test, x="True", y="Predicted", scatter=False, ax=g.ax_joint,
                   color=palette['Test'], label=f'Test (R² = {r2_test_score:.3f})', line_kws={'linewidth': 2})
        
        g.plot_marginals(sns.histplot, kde=False, element='bars', multiple='stack', alpha=0.7)
        
        # 移除上方和右方边缘图的网格线
        g.ax_marg_x.grid(False)
        g.ax_marg_y.grid(False)

        ax = g.ax_joint
        ax.plot([data['True'].min(), data['True'].max()], [data['True'].min(), data['True'].max()],
               c="black", alpha=0.7, linestyle='--', linewidth=2, label='Perfect Prediction')
        
        ax.set_xlabel('True Values', fontsize=14, fontweight='bold')
        ax.set_ylabel('Predicted Values', fontsize=14, fontweight='bold')
        ax.tick_params(axis='both', labelsize=12)
        ax.legend(loc='upper left', fontsize=12)
        ax.grid(alpha=0.3)
        
        plt.tight_layout()
        filename = f'{safe_filename(model_name)}_performance_jointplot.png'
        filepath = os.path.join(output_folder, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        print(f"JointGrid plot for {model_name} saved to: {filepath}")
        return filepath
    except Exception as e:
        print(f"Error creating JointGrid plot for {model_name}: {e}")
        return None

def plot_model_comparison_results(results_df, output_folder):
    """
    Generates and saves a bar chart comparing the performance of different models.
    """
    print("\n--- Generating Model Performance Comparison Plot ---")
    results_df = results_df.sort_values('Test R2', ascending=False)
    
    plt.figure(figsize=(14, 8))
    n_models = len(results_df)
    palette = plt.cm.viridis(np.linspace(0.2, 0.9, n_models))

    # Convert palette to list to avoid numpy array warning
    palette_list = [palette[i] for i in range(len(palette))]

    ax = sns.barplot(x='Test R2', y='Model', data=results_df, palette=palette_list, hue='Model', legend=False)
    
    ax.set_title('Comparison of Model Performance (Test Set R² Score)', fontsize=16, fontweight='bold')
    ax.set_xlabel('R² Score', fontsize=12)
    ax.set_ylabel('Model', fontsize=12)
    ax.set_xlim(left=max(0, results_df['Test R2'].min() - 0.05) if not results_df.empty else 0)
    
    for container in ax.containers:
        ax.bar_label(container, fmt='%.4f', fontsize=10, padding=3)
        
    plt.tight_layout()
    filepath = os.path.join(output_folder, 'model_comparison_results.png')
    plt.savefig(filepath, dpi=300)
    plt.close()
    print(f"Model comparison plot saved to: {filepath}")
    return filepath

def plot_train_test_r2_comparison(results_df, output_folder):
    """
    Generates a comparison plot for training R² and test R².
    """
    print("\n--- Generating Train vs Test R² Comparison Plot ---")
    valid_results = results_df[results_df['Test R2'] != -999].copy()
    if valid_results.empty:
        print("No valid results to plot for train-test comparison.")
        return None
    valid_results = valid_results.sort_values('Test R2', ascending=True)

    fig, ax = plt.subplots(figsize=(14, 8))
    y_pos = np.arange(len(valid_results))
    width = 0.35
    
    bars1 = ax.barh(y_pos - width/2, valid_results['Train R2'], width,
                   label='Train R²', color='#440154', alpha=0.8)
    bars2 = ax.barh(y_pos + width/2, valid_results['Test R2'], width,
                   label='Test R²', color='#21908c', alpha=0.8)
                   
    ax.set_yticks(y_pos)
    ax.set_yticklabels(valid_results['Model'], fontsize=12, fontweight='bold')
    ax.set_xlabel('R² Score', fontsize=14, fontweight='bold')
    ax.set_title('Model Performance: Train vs Test R² Comparison', fontsize=18, fontweight='bold')
    ax.legend(fontsize=12, loc='lower right')

    for bar in bars1:
        ax.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2, f'{bar.get_width():.3f}', ha='left', va='center', fontsize=10)
    for bar in bars2:
        ax.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2, f'{bar.get_width():.3f}', ha='left', va='center', fontsize=10)

    max_r2 = max(valid_results['Train R2'].max(), valid_results['Test R2'].max())
    ax.set_xlim(0, max_r2 + 0.15)
    
    plt.tight_layout()
    filepath = os.path.join(output_folder, 'model_train_test_r2_comparison.png')
    plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    print(f"Train vs Test R² comparison plot saved to: {filepath}")
    return filepath