import json
import os
import pandas as pd

def save_full_analysis_summary(results_df, output_folder):
    """
    Loads analysis results from a JSON file (now a list of records) and generates a text summary.
    results_dir_for_summary: The directory where 'full_analysis_summary.json' is located.
    feature_definitions: A dictionary containing detailed explanations for each feature.
    """
    summary_parts = []
    summary_parts.append("## Data Analysis Summary\n")

    # Add Feature Definitions to the summary
    summary_parts.append("### Feature Definitions:\n")
    for feature, definition in feature_definitions.items():
        summary_parts.append(f"- **{feature}**: {definition}\n")
    summary_parts.append("\n")

    full_summary_path = os.path.join(results_dir_for_summary, "full_analysis_summary.json")

    if not os.path.exists(full_summary_path):
        warning_msg = f"Warning: Analysis results file not found at {full_summary_path}. Cannot generate a detailed summary.\n"
        print(warning_msg)
        return f"Summary generation failed: Necessary analysis results file not found at '{full_summary_path}'."

    try:
        with open(full_summary_path, 'r', encoding='utf-8') as f:
            results_list = json.load(f)

        if not isinstance(results_list, list) or not results_list:
            summary_parts.append("- No valid model evaluation results found.\n")
            return "\n".join(summary_parts)

        # Convert the list of records directly into a DataFrame
        results_df = pd.DataFrame(results_list)
        
        # Ensure the required column exists
        if 'Test R2' not in results_df.columns:
            summary_parts.append("- Model evaluation results are in an unexpected format (missing 'Test R2' column).\n")
            return "\n".join(summary_parts)

        # --- Model Evaluation Summary ---
        summary_parts.append("### Systematic Model Evaluation:\n")
        
        # Find the best model based on Test R2 score
        best_model_row = results_df.loc[results_df['Test R2'].idxmax()]
        best_model_name = best_model_row.get('Model', 'N/A')
        best_r2_score = best_model_row.get('Test R2', -1.0)
        
        summary_parts.append(f"- A total of {len(results_df)} models were systematically tuned and evaluated.\n")
        summary_parts.append(f"- **The best performing model is {best_model_name}**, with a test set **R² score of: {best_r2_score:.4f}**.\n")
        
        summary_parts.append("- Performance overview of top models (Test R²):\n")
        sorted_df = results_df.sort_values('Test R2', ascending=False)
        for _, row in sorted_df.head().iterrows():
             summary_parts.append(f"  - {row['Model']}: {row['Test R2']:.4f}\n")

        # --- Interpretation Summary ---
        summary_parts.append("\n### Model Interpretation (SHAP & PDP):\n")
        summary_parts.append(f"- In-depth feature importance and dependence analysis was performed for the best models.\n")
        
        # Load SHAP feature importance if available
        shap_importance_path = os.path.join(results_dir_for_summary, f"{best_model_name}_shap_importance.json")
        if os.path.exists(shap_importance_path):
            with open(shap_importance_path, 'r', encoding='utf-8') as f:
                shap_importance = json.load(f)
            
            summary_parts.append("- **SHAP Feature Importance**:\n")
            for feature, importance in shap_importance.items():
                summary_parts.append(f"  - **{feature}**: {importance:.4f}\n")
        
        summary_parts.append(f"- SHAP and PDP plots have been generated for **{best_model_name}** and the **Stacking** model, revealing key drivers of silicon yield.\n")

        # --- Visualization Paths Summary ---
        summary_parts.append("\n### Key Visualizations Generated:\n")
        summary_parts.append("- All charts, including model comparisons, SHAP, and PDP plots, are saved in the output directory.\n")

    except FileNotFoundError:
        summary_parts.append(f"ERROR: Analysis results file {full_summary_path} not found.\n")
    except json.JSONDecodeError:
        summary_parts.append(f"ERROR: Failed to parse the analysis results file {full_summary_path}.\n")
    except Exception as e:
        summary_parts.append(f"An unknown error occurred during summary creation: {e}\n")
        
    return "\n".join(summary_parts)

def _print_model_metrics(metrics: dict, model_name: str = "Model"):
    """
    Prints model evaluation metrics in a standardized, publication-ready format.
    """
    print(f"\n--- {model_name} Model Evaluation ---")
    if isinstance(metrics, dict):
        for metric_name, value in metrics.items():
            if isinstance(value, float):
                print(f"  {metric_name.replace('_', ' ').title()}: {value:.4f}")
            else:
                print(f"  {metric_name.replace('_', ' ').title()}: {value}")
    else:
        print(f"  Warning: Metrics for {model_name} are not in a dictionary format: {metrics}")