import json
import os
import pandas as pd

def save_full_analysis_summary(results_df, output_folder):
    """
    Generates a text summary based on the results DataFrame and saves it to the output folder.
    results_df: DataFrame containing model evaluation results
    output_folder: The directory where analysis results are stored
    """
    summary_parts = []
    summary_parts.append("## Data Analysis Summary\n")

    # Define basic feature definitions (can be expanded)
    feature_definitions = {
        'SiO2': 'Silicon dioxide content in the raw material',
        'Al2O3': 'Aluminum oxide content in the raw material',
        'Phase': 'Physical phase of the material',
        'Additives': 'Type of chemical additives used',
        'Additive_Ratio': 'Ratio of additives to base material',
        'water': 'Water content or presence',
        'Na2CO3': 'Sodium carbonate concentration',
        'Ca(OH)2': 'Calcium hydroxide concentration',
        'Temp': 'Processing temperature',
        'Time': 'Processing time duration',
        'granular': 'Granular form or particle size',
        'Effective_Silicon': 'Target variable - effective silicon content'
    }

    # Add Feature Definitions to the summary
    summary_parts.append("### Feature Definitions:\n")
    for feature, definition in feature_definitions.items():
        summary_parts.append(f"- **{feature}**: {definition}\n")
    summary_parts.append("\n")

    try:
        # Check if results_df is valid
        if results_df is None or results_df.empty:
            summary_parts.append("- No model evaluation results available.\n")
        elif 'Test R2' not in results_df.columns:
            summary_parts.append("- Model evaluation results are in an unexpected format (missing 'Test R2' column).\n")
        else:
            # --- Model Evaluation Summary ---
            summary_parts.append("### Systematic Model Evaluation:\n")

            # Find the best model based on Test R2 score
            best_model_row = results_df.loc[results_df['Test R2'].idxmax()]
            best_model_name = best_model_row.get('Model', 'N/A')
            best_r2_score = best_model_row.get('Test R2', -1.0)

            summary_parts.append(f"- A total of {len(results_df)} models were systematically tuned and evaluated.\n")
            summary_parts.append(f"- **The best performing model is {best_model_name}**, with a test set **R² score of: {best_r2_score:.4f}**.\n")

            summary_parts.append("- Performance overview of top models (Test R²):\n")
            sorted_df = results_df.sort_values('Test R2', ascending=False)
            for _, row in sorted_df.head().iterrows():
                summary_parts.append(f"  - {row['Model']}: {row['Test R2']:.4f}\n")

            # --- Interpretation Summary ---
            summary_parts.append("\n### Model Interpretation (SHAP & PDP):\n")
            summary_parts.append(f"- In-depth feature importance and dependence analysis was performed for the best models.\n")
            summary_parts.append(f"- SHAP and PDP plots have been generated for **{best_model_name}** and other models, revealing key drivers of silicon yield.\n")

        # --- Visualization Paths Summary ---
        summary_parts.append("\n### Key Visualizations Generated:\n")
        summary_parts.append("- All charts, including model comparisons, SHAP, and PDP plots, are saved in the output directory.\n")

    except Exception as e:
        summary_parts.append(f"An error occurred during summary creation: {e}\n")

    # Save the summary to a file
    summary_text = "\n".join(summary_parts)
    summary_file_path = os.path.join(output_folder, "analysis_summary.md")

    try:
        with open(summary_file_path, 'w', encoding='utf-8') as f:
            f.write(summary_text)
        print(f"Analysis summary saved to: {summary_file_path}")
    except Exception as e:
        print(f"Failed to save summary file: {e}")

    return summary_text



def _print_model_metrics(metrics: dict, model_name: str = "Model"):
    """
    Prints model evaluation metrics in a standardized, publication-ready format.
    """
    print(f"\n--- {model_name} Model Evaluation ---")
    if isinstance(metrics, dict):
        for metric_name, value in metrics.items():
            if isinstance(value, float):
                print(f"  {metric_name.replace('_', ' ').title()}: {value:.4f}")
            else:
                print(f"  {metric_name.replace('_', ' ').title()}: {value}")
    else:
        print(f"  Warning: Metrics for {model_name} are not in a dictionary format: {metrics}")