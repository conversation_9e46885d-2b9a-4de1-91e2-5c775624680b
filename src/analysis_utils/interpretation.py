import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.inspection import PartialDependenceDisplay
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor

# 检查可选依赖的可用性
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
    print("Warning: SHAP library not found. SHAP-related functionalities will be disabled.")

try:
    import xgboost as xgb
    XGB_AVAILABLE = True
except ImportError:
    XGB_AVAILABLE = False
    xgb = None

try:
    from lightgbm import LGBMRegressor
    LGBM_AVAILABLE = True
except ImportError:
    LGBM_AVAILABLE = False
    LGBMRegressor = None

try:
    from catboost import CatBoostRegressor
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False
    CatBoostRegressor = None

# PDP 功能检查
try:
    from sklearn.inspection import PartialDependenceDisplay
    PDP_AVAILABLE = True
except ImportError:
    PDP_AVAILABLE = False
    print("Warning: PartialDependenceDisplay not available. PDP analysis will be disabled.")

def safe_filename(name):
    """清理文件名，只保留字母、数字和空格，并去除末尾空格"""
    return "".join([c for c in name if c.isalpha() or c.isdigit() or c.isspace() or c in ['_', '-']]).rstrip()

# 向后兼容的简化函数
def create_robust_shap_explainer(model, X_background, model_name="Unknown"):
    """为不同类型的模型创建合适的 SHAP 解释器（向后兼容）"""
    if not SHAP_AVAILABLE:
        return None

    try:
        # 树模型使用 TreeExplainer
        if isinstance(model, (RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor)):
            return shap.TreeExplainer(model)
        elif XGB_AVAILABLE and xgb and isinstance(model, xgb.XGBRegressor):
            return shap.TreeExplainer(model)
        elif LGBM_AVAILABLE and LGBMRegressor and isinstance(model, LGBMRegressor):
            return shap.TreeExplainer(model)
        elif CATBOOST_AVAILABLE and CatBoostRegressor and isinstance(model, CatBoostRegressor):
            return shap.TreeExplainer(model)
        else:
            # 其他模型使用 KernelExplainer
            if isinstance(X_background, pd.DataFrame):
                background_sample = X_background.sample(min(50, len(X_background)), random_state=42) if len(X_background) > 50 else X_background
                return shap.KernelExplainer(model.predict, background_sample.values)
            else:
                sample_size = min(50, len(X_background))
                indices = np.random.choice(len(X_background), sample_size, replace=False)
                background_sample = X_background[indices]
                return shap.KernelExplainer(model.predict, background_sample)
    except Exception as e:
        print(f"Warning: Could not create SHAP explainer for {model_name}: {e}")
        return None

def robust_shap_analysis(model, X_train, X_test, feature_names, output_folder, model_name="Model"):
    """
    执行完整的 SHAP 分析，包括解释器创建和可视化生成
    """
    if not SHAP_AVAILABLE:
        print("SHAP not available, skipping SHAP analysis")
        return []

    plot_paths = []
    plot_filename_prefix = safe_filename(model_name)
    
    print(f"\n=== 开始为模型 {model_name} 执行SHAP分析 ===")
    print(f"模型类型: {type(model).__name__}")
    print(f"训练数据形状: {X_train.shape}")
    print(f"测试数据形状: {X_test.shape}")
    
    try:
        # 创建 SHAP 解释器
        explainer = None
        try:
            # 树模型使用 TreeExplainer
            if isinstance(model, (RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor)):
                print(f"使用 TreeExplainer 解释 {type(model).__name__} 模型")
                explainer = shap.TreeExplainer(model)
            elif hasattr(xgb, 'XGBRegressor') and isinstance(model, xgb.XGBRegressor):
                print(f"使用 TreeExplainer 解释 XGBRegressor 模型")
                explainer = shap.TreeExplainer(model)
            elif hasattr(LGBMRegressor, '__name__') and isinstance(model, LGBMRegressor):
                print(f"使用 TreeExplainer 解释 LGBMRegressor 模型")
                explainer = shap.TreeExplainer(model)
            elif hasattr(CatBoostRegressor, '__name__') and isinstance(model, CatBoostRegressor):
                print(f"使用 TreeExplainer 解释 CatBoostRegressor 模型")
                explainer = shap.TreeExplainer(model)
            else:
                # 其他模型使用 KernelExplainer
                print(f"使用 KernelExplainer 解释 {type(model).__name__} 模型")
                background_sample = X_train.sample(min(50, len(X_train)), random_state=42) if len(X_train) > 50 else X_train
                print(f"背景样本形状: {background_sample.shape}")
                
                # 检查并处理NaN值
                if background_sample.isnull().any().any():
                    print("警告: 背景样本中存在NaN值，正在填充为0")
                    background_sample = background_sample.fillna(0)
                
                explainer = shap.KernelExplainer(model.predict, background_sample.values)
        except Exception as e:
            print(f"❌ 无法为 {model_name} 创建SHAP解释器: {e}")
            import traceback
            traceback.print_exc()
            return []

        if explainer is None:
            print(f"❌ 无法为 {model_name} 创建SHAP解释器")
            return []

        # 限制测试样本数量以提高性能
        test_sample = X_test.sample(min(100, len(X_test)), random_state=42) if len(X_test) > 100 else X_test
        print(f"测试样本形状: {test_sample.shape}")

        # 确保测试样本也没有 NaN 值
        if test_sample.isnull().any().any():
            print("警告: 测试样本中存在NaN值，正在填充为0")
            test_sample = test_sample.fillna(0)

        # 计算 SHAP 值
        print(f"正在计算 {model_name} 的SHAP值...")
        try:
            if isinstance(explainer, shap.TreeExplainer):
                # TreeExplainer 可以直接使用 DataFrame
                shap_values = explainer.shap_values(test_sample)
            else:
                # KernelExplainer 使用 numpy 数组
                shap_values = explainer.shap_values(test_sample.values)
            
            print(f"✅ SHAP值计算完成，形状: {np.array(shap_values).shape}")
        except Exception as e:
            print(f"❌ 计算SHAP值时出错: {e}")
            import traceback
            traceback.print_exc()
            return []

        # 生成 SHAP 条形图
        try:
            print(f"正在创建 {model_name} 的SHAP条形图...")
            plt.figure(figsize=(10, 8))
            shap.summary_plot(shap_values, test_sample, plot_type="bar", show=False)
            plt.title(f'SHAP Feature Importance - {model_name}', fontsize=16, fontweight='bold')
            plt.tight_layout()
            bar_path = os.path.join(output_folder, f'shap_summary_bar_{plot_filename_prefix}.png')
            plt.savefig(bar_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            plot_paths.append(bar_path)
            
            # 验证文件是否真的被创建
            if os.path.exists(bar_path):
                print(f"✅ SHAP条形图已保存: {bar_path} (大小: {os.path.getsize(bar_path)} 字节)")
            else:
                print(f"❌ 文件未找到: {bar_path}")
        except Exception as e:
            print(f"❌ 创建SHAP条形图时出错: {e}")
            import traceback
            traceback.print_exc()

        # 生成 SHAP 散点图
        try:
            print(f"Creating SHAP dot plot for {model_name}...")
            plt.figure(figsize=(10, 8))
            shap.summary_plot(shap_values, test_sample, show=False)
            plt.title(f'SHAP Summary Plot - {model_name}', fontsize=16, fontweight='bold')
            plt.tight_layout()
            dot_path = os.path.join(output_folder, f'shap_summary_dot_{plot_filename_prefix}.png')
            plt.savefig(dot_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            plot_paths.append(dot_path)
            print(f"✅ SHAP dot plot saved: {dot_path}")

            # 验证文件是否真的被创建
            if os.path.exists(dot_path):
                print(f"✅ File verified: {dot_path} (size: {os.path.getsize(dot_path)} bytes)")
            else:
                print(f"❌ File not found: {dot_path}")
        except Exception as e:
            print(f"❌ Error creating SHAP dot plot for {model_name}: {e}")
            import traceback
            traceback.print_exc()

        # 生成前3个重要特征的依赖图
        try:
            feature_importance = np.abs(shap_values).mean(0)
            n_features = min(3, len(feature_importance))  # 确保不超过特征数量
            top_features_idx = np.argsort(feature_importance)[-n_features:]  # 前n个重要特征

            for i, feature_idx in enumerate(top_features_idx):
                try:
                    # 确保索引在有效范围内
                    if feature_idx < len(feature_names):
                        feature_name = feature_names[feature_idx]
                    else:
                        feature_name = f"Feature_{feature_idx}"

                    plt.figure(figsize=(10, 6))
                    shap.dependence_plot(feature_idx, shap_values, test_sample, show=False)
                    plt.title(f'SHAP Dependence - {feature_name} ({model_name})', fontsize=14, fontweight='bold')
                    plt.tight_layout()
                    dep_path = os.path.join(output_folder, f'shap_dependence_{plot_filename_prefix}_{i}_{safe_filename(feature_name)}.png')
                    plt.savefig(dep_path, dpi=300, bbox_inches='tight', facecolor='white')
                    plt.close()
                    plot_paths.append(dep_path)
                    print(f"SHAP dependence plot saved: {dep_path}")
                except Exception as dep_e:
                    print(f"Error creating dependence plot for feature {feature_idx}: {dep_e}")
                    continue
        except Exception as e:
            print(f"Error creating SHAP dependence plots for {model_name}: {e}")

    except Exception as e:
        print(f"Error in SHAP analysis for {model_name}: {e}")

    return plot_paths

# 向后兼容的别名函数
def robust_pdp_analysis(model, X_data, feature_names, output_folder, model_name="Model"):
    """向后兼容的 PDP 分析函数"""
    return create_pdp_analysis(model, X_data, feature_names, output_folder, model_name)

def _plot_shap_summary_plot(shap_values, X_test_df, output_folder, model_name_str):
    """
    生成并保存 SHAP 摘要图 (条形图和散点图)，并返回路径列表。
    """
    if not SHAP_AVAILABLE: return []
    
    plot_paths = []
    plot_filename_prefix = safe_filename(model_name_str)

    # SHAP 摘要条形图
    try:
        plt.figure(figsize=(10, 8))
        shap.summary_plot(shap_values, X_test_df, plot_type="bar", show=False)
        plt.title(f'SHAP 特征重要性 - {model_name_str}', fontsize=18, fontweight='bold')
        ax_bar = plt.gca()
        ax_bar.tick_params(axis='both', labelsize=12)
        ax_bar.set_xlabel(ax_bar.get_xlabel(), fontsize=14, fontweight='bold')
        plt.tight_layout()
        shap_bar_path = os.path.join(output_folder, f'shap_summary_plot_bar_{plot_filename_prefix}.png')
        plt.savefig(shap_bar_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        print(f"{model_name_str} 的 SHAP 条形图已保存到 {shap_bar_path}")
        plot_paths.append(shap_bar_path)
    except Exception as e:
        print(f"为 {model_name_str} 创建 SHAP 条形图时出错: {e}")

    # SHAP 摘要散点图
    try:
        plt.figure(figsize=(10, 8))
        shap.summary_plot(shap_values, X_test_df, show=False)
        plt.title(f'SHAP 摘要图 - {model_name_str}', fontsize=14, fontweight='bold')
        plt.tight_layout()
        shap_dot_path = os.path.join(output_folder, f'shap_summary_plot_dot_{plot_filename_prefix}.png')
        plt.savefig(shap_dot_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        print(f"{model_name_str} 的 SHAP 散点图已保存到 {shap_dot_path}")
        plot_paths.append(shap_dot_path)
    except Exception as e:
        print(f"为 {model_name_str} 创建 SHAP 散点图时出错: {e}")
    
    return plot_paths

def create_pdp_analysis(model_to_explain, X_data, feature_names, output_folder, model_name_str=""):
    """
    创建增强的偏依赖图 (Partial Dependence Plot) 分析 - 统一版本
    """
    if not PDP_AVAILABLE:
        print("警告: PDP 功能不可用，跳过 PDP 分析")
        return None

    # 统一参数处理
    model_name = model_name_str or "Model"
    plot_filename_prefix = safe_filename(model_name)
    print(f"\n--- Creating Enhanced Partial Dependence Plot Analysis for {model_name} ---")

    try:
        # 确保数据是 DataFrame 格式
        if not isinstance(X_data, pd.DataFrame):
            X_df_pdp = pd.DataFrame(X_data, columns=feature_names)
        else:
            X_df_pdp = X_data.copy()

        # 确保 PDP 数据没有 NaN 值
        if X_df_pdp.isnull().any().any():
            print(f"Warning: Found NaN values in PDP data for {model_name}, filling with 0...")
            X_df_pdp = X_df_pdp.fillna(0)

        n_display_features = min(6, len(feature_names))
        features_for_pdp = feature_names[:n_display_features]

        ncols = 3
        nrows = (n_display_features + ncols - 1) // ncols
        fig, axes = plt.subplots(nrows=nrows, ncols=ncols, figsize=(18, 6 * nrows), squeeze=False)
        axes = axes.flatten()

        success_count = 0
        for idx, feature in enumerate(features_for_pdp):
            ax = axes[idx]
            try:
                # 检查特征是否存在且有变化
                if feature not in X_df_pdp.columns:
                    ax.text(0.5, 0.5, f'Feature {feature}\nnot found', ha='center', va='center', color='red')
                    continue

                feature_values = X_df_pdp[feature]
                if feature_values.nunique() < 2:
                    ax.text(0.5, 0.5, f'Feature {feature}\nhas no variation', ha='center', va='center', color='orange')
                    continue

                # 创建 PDP - 使用适中的参数
                PartialDependenceDisplay.from_estimator(
                    model_to_explain, X_df_pdp, [feature], ax=ax, n_jobs=1, grid_resolution=75
                )
                ax.set_title(f'{feature}', fontsize=14, fontweight='bold')
                ax.set_xlabel(feature, fontsize=12, fontweight='bold')
                ax.set_ylabel('Partial Dependence', fontsize=12, fontweight='bold')
                success_count += 1

            except Exception as e:
                ax.text(0.5, 0.5, f'PDP Failed\n{feature}\n{str(e)[:20]}...',
                       ha='center', va='center', color='red', fontsize=10)
                print(f"    ERROR generating PDP for {feature}: {e}")

        # 隐藏未使用的子图
        for idx in range(n_display_features, len(axes)):
            axes[idx].set_visible(False)

        # 设置标题显示成功率
        fig.suptitle(f'Partial Dependence Analysis - {model_name} ({success_count}/{n_display_features} successful)',
                    fontsize=18, fontweight='bold', y=0.98)
        plt.tight_layout(rect=[0, 0, 1, 0.95])

        # 保存文件 - 使用统一的命名格式
        pdp_path = os.path.join(output_folder, f'{plot_filename_prefix}_partial_dependence_plots.png')
        plt.savefig(pdp_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close(fig)

        if success_count > 0:
            print(f"📊 Overall PDP plot saved to: {pdp_path} ({success_count}/{n_display_features} features successful)")
            return pdp_path
        else:
            print(f"PDP analysis failed for all features in {model_name}")
            return None

    except Exception as e:
        print(f"Warning: Could not create PDP analysis for {model_name}: {e}")
        return None

def explain_stacking_model_fully(stacking_model, X_train_processed, X_test_processed_df, preprocessor, output_folder, y_train, y_test):
    """
    Performs a multi-level SHAP and PDP analysis of a Stacking model.
    """
    print("\n" + "#"*80)
    print("STARTING MULTI-LEVEL STACKING MODEL INTERPRETATION")
    print("#"*80)

    if not SHAP_AVAILABLE:
        print("SHAP not available. Skipping stacking model explanation.")
        return

    # 确保数据格式正确
    if isinstance(X_train_processed, pd.DataFrame):
        X_train_processed_df = X_train_processed.copy()
        feature_names = X_train_processed_df.columns.tolist()
    else:
        feature_names = X_test_processed_df.columns.tolist()
        X_train_processed_df = pd.DataFrame(X_train_processed, columns=feature_names)

    # 检查并处理 NaN 值 - 使用更稳健的方法
    print("Checking and cleaning NaN values for Stacking model compatibility...")

    # 对训练数据进行 NaN 处理
    if X_train_processed_df.isnull().any().any():
        print("Warning: Found NaN values in training data, filling with median/mode...")
        # 数值列用中位数填充
        numeric_cols = X_train_processed_df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            median_val = X_train_processed_df[col].median()
            if pd.isna(median_val):  # 如果中位数也是 NaN，用 0 填充
                median_val = 0.0
            X_train_processed_df[col] = X_train_processed_df[col].fillna(median_val)

        # 分类列用众数填充
        categorical_cols = X_train_processed_df.select_dtypes(exclude=[np.number]).columns
        for col in categorical_cols:
            mode_val = X_train_processed_df[col].mode()
            fill_val = mode_val[0] if not mode_val.empty else 0
            X_train_processed_df[col] = X_train_processed_df[col].fillna(fill_val)

    # 对测试数据进行 NaN 处理
    if X_test_processed_df.isnull().any().any():
        print("Warning: Found NaN values in test data, filling with median/mode...")
        # 数值列用训练数据的中位数填充
        numeric_cols = X_test_processed_df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col in X_train_processed_df.columns:
                median_val = X_train_processed_df[col].median()
                if pd.isna(median_val):
                    median_val = 0.0
            else:
                median_val = X_test_processed_df[col].median()
                if pd.isna(median_val):
                    median_val = 0.0
            X_test_processed_df[col] = X_test_processed_df[col].fillna(median_val)

        # 分类列用训练数据的众数填充
        categorical_cols = X_test_processed_df.select_dtypes(exclude=[np.number]).columns
        for col in categorical_cols:
            if col in X_train_processed_df.columns:
                mode_val = X_train_processed_df[col].mode()
                fill_val = mode_val[0] if not mode_val.empty else 0
            else:
                mode_val = X_test_processed_df[col].mode()
                fill_val = mode_val[0] if not mode_val.empty else 0
            X_test_processed_df[col] = X_test_processed_df[col].fillna(fill_val)

    # 最后检查：确保没有任何 NaN 值残留
    if X_train_processed_df.isnull().any().any():
        print("Final cleanup: Replacing any remaining NaN values with 0...")
        X_train_processed_df = X_train_processed_df.fillna(0)

    if X_test_processed_df.isnull().any().any():
        print("Final cleanup: Replacing any remaining NaN values with 0...")
        X_test_processed_df = X_test_processed_df.fillna(0)

    print(f"Data cleaning complete. Train shape: {X_train_processed_df.shape}, Test shape: {X_test_processed_df.shape}")

    base_models = {name: est for name, est in stacking_model.named_estimators_.items()}

    # --- Level 1: Base Learner SHAP Analysis ---
    print("\n--- Level 1: Analyzing Base Learners ---")
    num_models = len(base_models)
    ncols = 2
    nrows = (num_models + ncols - 1) // ncols
    fig_base, axes_base = plt.subplots(nrows=nrows, ncols=ncols, figsize=(18, 5 * nrows), squeeze=False)
    axes_flat = axes_base.flatten()

    for idx, (name, model) in enumerate(base_models.items()):
        ax = axes_flat[idx]
        try:
            from sklearn.metrics import r2_score # Import here to ensure it's available

            # 在测试集上评估真实基学习器的性能
            # 使用原始数组格式避免特征名不匹配问题
            y_test_pred = model.predict(X_test_processed_df.values)
            r2_score_val = r2_score(y_test, y_test_pred)

            # 为SHAP分析准备背景数据和样本数据
            background_sample = X_train_processed_df.sample(min(100, len(X_train_processed_df)), random_state=42)
            test_sample = X_test_processed_df.sample(min(50, len(X_test_processed_df)), random_state=42)

            # 确保样本数据没有 NaN 值
            if background_sample.isnull().any().any():
                background_sample = background_sample.fillna(0)
            if test_sample.isnull().any().any():
                test_sample = test_sample.fillna(0)

            # 创建 SHAP 解释器
            explainer = create_robust_shap_explainer(model, background_sample, name)

            if explainer is None:
                ax.text(0.5, 0.5, f'SHAP Explainer\nCreation Failed\nfor {name}',
                       ha='center', va='center', fontsize=12, color='red',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))
                ax.set_title(f'{name} (Failed)', fontsize=16, fontweight='bold')
                ax.set_xticks([])
                ax.set_yticks([])
                continue

            # 计算 SHAP 值
            # 使用原始数组格式避免特征名不匹配问题
            if isinstance(explainer, shap.TreeExplainer):
                shap_values = explainer.shap_values(test_sample.values)
            else:
                shap_values = explainer.shap_values(test_sample.values)

            # 计算特征重要性并创建条形图
            feature_importance = np.abs(shap_values).mean(0)
            top_n = min(5, len(feature_importance)) # 更改为前5个
            sorted_idx = np.argsort(feature_importance)[-top_n:]

            # 使用viridis渐变色 - 论文标准
            colors = plt.cm.viridis(np.linspace(0.3, 0.8, len(sorted_idx)))
            y_pos = np.arange(len(sorted_idx))

            ax.barh(y_pos, feature_importance[sorted_idx],
                   color=colors, alpha=0.8, edgecolor='#7e7e7e', linewidth=0.5)

            # 设置标签 - 论文标准字体大小
            feature_labels = [feature_names[i] for i in sorted_idx]
            ax.set_yticks(y_pos)
            ax.set_yticklabels(feature_labels, fontsize=12, fontweight='bold')
            ax.set_xlabel('Mean |SHAP Value|', fontsize=14, fontweight='bold')
            ax.set_title(f'{name}\n(R² = {r2_score_val:.3f})', fontsize=16, fontweight='bold') # 添加R²分数
            ax.grid(axis='x', alpha=0.3)
            ax.tick_params(axis='x', labelsize=12)
        except Exception as e:
            ax.text(0.5, 0.5, f'SHAP Failed for {name}\n{str(e)[:30]}...',
                   ha='center', va='center', fontsize=12, color='red',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))
            ax.set_title(f'{name} (Failed)', fontsize=16, fontweight='bold')
            ax.set_xticks([])
            ax.set_yticks([])
            print(f"    ERROR generating SHAP for base learner {name}: {e}")
    
    # Hide any unused subplots
    for i in range(idx + 1, len(axes_flat)):
        axes_flat[i].set_visible(False)

    # 添加整体标题 - 论文标准
    fig_base.suptitle('Base Learners SHAP Feature Importance Analysis',
                         fontsize=20, fontweight='bold', y=0.98)

    plt.tight_layout(rect=[0, 0, 1, 0.95])  # 为标题留出空间
    path = os.path.join(output_folder, 'stacking_level1_base_learners_shap.png')
    plt.savefig(path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig_base)
    print(f"Base learners SHAP plots saved to: {path}")

    # --- Level 2: Meta-Learner SHAP Analysis ---
    print("\n--- Level 2: Analyzing the Meta-Learner ---")
    try:
        base_predictions_test = stacking_model.transform(X_test_processed_df.values)
        base_predictions_train = stacking_model.transform(X_train_processed_df.values)

        # 检查并处理 NaN 值
        if np.isnan(base_predictions_test).any():
            print("Warning: Found NaN in base predictions test, filling with 0...")
            base_predictions_test = np.nan_to_num(base_predictions_test, nan=0.0)

        if np.isnan(base_predictions_train).any():
            print("Warning: Found NaN in base predictions train, filling with 0...")
            base_predictions_train = np.nan_to_num(base_predictions_train, nan=0.0)

        meta_model = stacking_model.final_estimator_
        meta_feature_names = list(base_models.keys())

        meta_explainer = shap.LinearExplainer(meta_model, pd.DataFrame(base_predictions_train, columns=meta_feature_names))
        meta_shap_values = meta_explainer(pd.DataFrame(base_predictions_test, columns=meta_feature_names))

        plt.figure(figsize=(12, 8))
        shap.summary_plot(meta_shap_values, pd.DataFrame(base_predictions_test, columns=meta_feature_names), plot_type='bar', show=False)
        plt.title('Meta-Learner SHAP Analysis', fontsize=18, fontweight='bold')
        plt.tight_layout()
        path = os.path.join(output_folder, 'stacking_level2_meta_learner_shap_bar.png')
        plt.savefig(path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        print(f"Meta-learner SHAP bar plot saved to: {path}")
    except Exception as e:
        print(f"    ERROR generating Meta-Learner SHAP analysis: {e}")

    # --- Level 3: Overall Stacking Model as Black-Box ---
    print("\n--- Level 3: Analyzing Entire Stacking Model as a Black-Box ---")
    try:
        # 创建一个包装函数来处理 Stacking 模型的预测
        def stacking_predict_wrapper(X):
            # 确保输入是 numpy 数组
            if isinstance(X, pd.DataFrame):
                X = X.values
            return stacking_model.predict(X)

        # 使用 KernelExplainer 进行整体分析
        background_sample = X_train_processed_df.sample(min(50, len(X_train_processed_df)), random_state=42)
        test_sample = X_test_processed_df.sample(min(50, len(X_test_processed_df)), random_state=42)

        # 确保样本数据没有 NaN 值
        if background_sample.isnull().any().any():
            print("Cleaning background sample NaN values...")
            background_sample = background_sample.fillna(0)

        if test_sample.isnull().any().any():
            print("Cleaning test sample NaN values...")
            test_sample = test_sample.fillna(0)

        explainer = shap.KernelExplainer(stacking_predict_wrapper, background_sample.values)
        shap_values = explainer.shap_values(test_sample.values)

        # 生成 SHAP 图
        shap_plot_paths = _plot_shap_summary_plot(shap_values, test_sample, output_folder, "Stacking_Overall")
        print(f"Generated {len(shap_plot_paths)} SHAP plots for Stacking Overall")

    except Exception as e:
        print(f"    ERROR generating overall Stacking SHAP analysis: {e}")

    # --- PDP for Overall Stacking Model ---
    print("\n--- PDP Analysis for the Overall Stacking Model ---")
    try:
        # 确保 PDP 分析使用的数据没有 NaN 值
        X_pdp_data = X_test_processed_df.copy()
        if X_pdp_data.isnull().any().any():
            print("Cleaning PDP data NaN values...")
            X_pdp_data = X_pdp_data.fillna(0)

        # 使用原始的 PDP 分析函数
        create_pdp_analysis(stacking_model, X_pdp_data, feature_names, output_folder, model_name_str="Stacking_Overall")
    except Exception as e:
        print(f"    ERROR generating PDP analysis for Stacking Overall: {e}")