import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
import os
from itertools import combinations
from matplotlib.colors import Normalize
from matplotlib.patches import Circle, Wedge

# --- Unified Plotting Style Configuration ---
# Use a professional and publication-ready style for consistency and aesthetics.
plt.style.use('seaborn-v0_8-whitegrid')

# Update font settings for clarity and consistency in publications.
plt.rcParams.update({
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans'],
    'font.size': 14,
    'axes.titlesize': 16,
    'axes.labelsize': 14,
    'xtick.labelsize': 14,
    'ytick.labelsize': 14,
    'legend.fontsize': 14,
    'figure.titlesize': 18,
    'axes.unicode_minus': False,
})

CONTINUOUS_CMAP = 'viridis'

def create_advanced_correlation_matrix(correlation_matrix, output_folder):
    """
    Creates an advanced correlation matrix visualization with pie chart elements.
    This improved version fixes graphic deformation and element overlap issues.
    
    Key Features:
    - Upper triangle: Shows correlation coefficient values
    - Lower triangle: Shows pie chart representations using Circle and Wedge patches
    - Diagonal: Shows feature names with colored backgrounds
    - Fixed aspect ratio and spacing to prevent overlapping
    
    Machine Learning Insights:
    - Identifies multicollinearity for feature selection
    - Reveals non-linear relationships for feature engineering
    - Guides dimensionality reduction strategies
    - Supports model interpretability analysis
    """
    print("🎨 Creating advanced correlation matrix visualization...")
    print("📊 This visualization helps identify:")
    print("   • Feature relationships and dependencies")
    print("   • Multicollinearity issues (|r| > 0.7)")
    print("   • Feature selection opportunities")
    print("   • Data quality and anomaly patterns")
    
    try:
        # Convert correlation matrix to numpy array and get feature names
        corr_data = correlation_matrix.values
        feature_names = correlation_matrix.columns.tolist()
        
        # Set up the plot aesthetics
        fig, ax = plt.subplots(figsize=(12, 10), facecolor='white')
        ax.set_facecolor('white')
        fig.suptitle("Silicon Material Parameter Correlation Matrix", fontsize=16, y=0.96, fontweight='bold')
        
        n = len(feature_names)
        
        # Define the colormap and normalization with symmetric range
        cmap = plt.get_cmap(CONTINUOUS_CMAP)
        # Use symmetric range for better color representation
        max_abs_corr = np.abs(corr_data).max()
        cmin, cmax = -max_abs_corr, max_abs_corr
        norm = Normalize(vmin=cmin, vmax=cmax)

        # Iterate through the matrix to draw elements
        for i in range(n):
            for j in range(n):
                corr_val = corr_data[i, j]
                color = cmap(norm(corr_val))

                # Upper triangle: Display pie-chart glyphs
                if i < j:
                    # Reduced radius to prevent overlap (key fix)
                    radius = 0.35
                    # Add the background circle with lighter color
                    circle = Circle((j, i), radius, facecolor='#F0F0F0', edgecolor='grey', linewidth=0.5)
                    ax.add_patch(circle)
                    
                    # Add the wedge representing the correlation value
                    # The angle of the wedge is proportional to the absolute correlation value
                    angle = abs(corr_val) * 360
                    
                    # Positive correlations go clockwise from 90 degrees
                    # Negative correlations go counter-clockwise from 90 degrees
                    if corr_val >= 0:
                        theta1 = 90
                        theta2 = 90 + angle
                    else:
                        theta1 = 90 - angle
                        theta2 = 90
                    
                    wedge = Wedge(center=(j, i), r=radius, theta1=theta1, theta2=theta2,
                                 facecolor=color, edgecolor='black', linewidth=0.5)
                    ax.add_patch(wedge)

                # Lower triangle: Display numerical values
                elif i > j:
                    ax.text(j, i, f'{corr_val:.2f}', ha='center', va='center',
                           color=color, fontsize=11, weight='bold')
                

        # Configure axes and labels with proper aspect ratio (key fix)
        ax.set_aspect('equal', adjustable='box')
        ax.set_xlim(-0.5, n - 0.5)
        ax.set_ylim(n - 0.5, -0.5)
        ax.set_xticks(np.arange(n))
        ax.set_yticks(np.arange(n))
        ax.set_xticklabels(feature_names, rotation=45, ha='right', fontsize=10)
        ax.set_yticklabels(feature_names, fontsize=10)
        ax.tick_params(axis='both', which='major', length=3, width=1, color='gray')
        
        # Remove spines and grid
        for spine in ax.spines.values():
            spine.set_visible(False)
        ax.grid(False) # Turn off grid lines

        # Add the color bar
        cbar_ax = fig.add_axes([0.92, 0.2, 0.03, 0.75])  # [left, bottom, width, height]
        sm = plt.cm.ScalarMappable(cmap=cmap, norm=norm)
        sm.set_array([])
        cbar = fig.colorbar(sm, cax=cbar_ax)
        cbar.set_label('Correlation Coefficient', size=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        plt.tight_layout()
        file_path = os.path.join(output_folder, 'advanced_correlation_matrix.png')
        plt.savefig(file_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close(fig) # Close the figure
        print(f"Advanced correlation matrix visualization completed and saved to {file_path}")
        return file_path # Return the path
        
    except Exception as e:
        print(f"Error creating advanced correlation matrix: {e}")
        print("Falling back to standard correlation matrix...")
        
        fig, ax = plt.subplots(figsize=(12, 10)) # Create new figure for fallback
        sns.heatmap(correlation_matrix, annot=True, cmap='viridis', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8}, ax=ax)
        plt.title('Correlation Matrix (Fallback)', fontsize=16, fontweight='bold')
        plt.tight_layout()
        fallback_file_path = os.path.join(output_folder, 'correlation_matrix_fallback.png')
        plt.savefig(fallback_file_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close(fig) # Close the figure
        print(f"Fallback correlation matrix saved to {fallback_file_path}")
        return fallback_file_path # Return the fallback path


# 2. Exploratory Data Analysis (EDA)
def perform_eda(df, target_column, output_folder):
    """
    Performs EDA on the dataframe.
    Returns a dictionary of generated plot paths.
    """
    print("\n--- Starting Exploratory Data Analysis (EDA) ---")
    eda_plot_paths = {}
    
    # --- Descriptive Statistics ---
    print("\n--- Descriptive Statistics ---")
    print(df.describe())
    
    # --- Target Variable Distribution ---
    print(f"\n--- Analyzing '{target_column}' Distribution ---")
    fig, ax = plt.subplots(figsize=(12, 6))
    # 使用更温暖的颜色 - 蓝绿色而不是紫色
    hist_color = '#2E8B57'  # Sea Green - 更专业的颜色
    kde_color = '#FF6B35'   # Orange Red - 与蓝绿色形成良好对比

    sns.histplot(df[target_column], kde=True, bins=30, color=hist_color,
                 alpha=0.7, ax=ax)
    # Add KDE line with contrasting color
    sns.kdeplot(df[target_column], color=kde_color, linewidth=3, ax=ax)

    # 使用论文标准字体大小
    ax.set_title(f'Distribution of {target_column}', fontsize=18, fontweight='bold')
    ax.set_xlabel(target_column, fontsize=14, fontweight='bold')
    ax.set_ylabel('Frequency', fontsize=14, fontweight='bold')
    ax.tick_params(axis='both', labelsize=12)
    ax.grid(alpha=0.3, color='#7e7e7e')

    file_path = os.path.join(output_folder, 'target_distribution.png')
    plt.savefig(file_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    print(f"Target distribution plot saved to {file_path}")
    eda_plot_paths['target_distribution_plot'] = file_path

    
    # --- Advanced Correlation Matrix Analysis ---
    print("\n=== CORRELATION ANALYSIS ===")
    print("Analyzing feature correlations to identify:")
    print("• Strong positive/negative relationships")
    print("• Potential multicollinearity issues")
    print("• Feature selection insights")
    print("• Data quality patterns")
    
    # Select only numeric columns for correlation matrix
    numeric_df = df.select_dtypes(include=np.number)
    if not numeric_df.empty:
        # 在计算相关性矩阵之前，排除目标变量
        features_for_corr = numeric_df.drop(columns=[target_column], errors='ignore')
        correlation_matrix = features_for_corr.corr()
        
        # Display key correlation insights
        print(f"\n--- Key Correlation Insights ---")
        print(f"• Total numeric features: {len(numeric_df.columns)}")
        
        if target_column in correlation_matrix:
            target_correlations = correlation_matrix[target_column].abs().sort_values(ascending=False)
            print(f"• Top 3 features correlated with {target_column}:")
            count = 0
            for feature, corr_val in target_correlations.items(): # Corrected variable name
                if feature != target_column and count < 3:
                    print(f"  {count+1}. {feature}: {corr_val:.3f}")
                    count +=1
        
        # Check for multicollinearity (high inter-feature correlations)
        high_corr_pairs = []
        for i in range(len(correlation_matrix.columns)):
            for j in range(i+1, len(correlation_matrix.columns)):
                if abs(correlation_matrix.iloc[i, j]) > 0.7:  # Threshold for high correlation
                    high_corr_pairs.append((
                        correlation_matrix.columns[i],
                        correlation_matrix.columns[j],
                        correlation_matrix.iloc[i, j]
                    ))
        
        if high_corr_pairs:
            print(f"\n• High correlation pairs (|r| > 0.7) - Potential multicollinearity:")
            for feat1, feat2, corr_val_pair in high_corr_pairs: # Corrected variable name
                print(f"  - {feat1} ↔ {feat2}: {corr_val_pair:.3f}")
        else:
            print(f"\n• No high correlation pairs found (|r| > 0.7)")
        
        # Generate the advanced correlation matrix visualization
        corr_matrix_path = create_advanced_correlation_matrix(correlation_matrix, output_folder)
        if corr_matrix_path:
            eda_plot_paths['advanced_correlation_matrix_plot'] = corr_matrix_path
    else:
        print("No numeric columns found for correlation analysis.")

    
    # --- Feature Interaction Analysis (Enhanced) ---
    if not numeric_df.empty and target_column in numeric_df.columns:
        print("\n--- Generating Feature Interaction Analysis ---")
        correlation_matrix_for_interaction = numeric_df.corr() # Recalculate if numeric_df was filtered
        target_corr = correlation_matrix_for_interaction[target_column].abs().sort_values(ascending=False)
        top_features = target_corr.index[1:4].tolist()  # Top 3 features excluding target itself
        
        if len(top_features) >= 2:
            fig_interaction, axes_interaction = plt.subplots(1, min(3, len(top_features)*(len(top_features)-1)//2), figsize=(18, 5), squeeze=False) # Ensure axes is 2D
            axes_flat = axes_interaction.flatten()
            plot_idx = 0
            
            # Generate unique pairs of top features
            feature_pairs = list(combinations(top_features, 2))

            for i, (feat1, feat2) in enumerate(feature_pairs):
                if i >= len(axes_flat): break # Avoid index out of bounds if fewer subplots than pairs
                
                ax_current = axes_flat[i]
                scatter = ax_current.scatter(df[feat1], df[feat2], c=df[target_column],
                                        cmap=CONTINUOUS_CMAP, alpha=0.6, s=50)
                ax_current.set_xlabel(feat1, fontsize=12, fontweight='bold')
                ax_current.set_ylabel(feat2, fontsize=12, fontweight='bold')
                ax_current.set_title(f'Interaction: {feat1} vs {feat2}', fontsize=12, fontweight='bold')
                ax_current.grid(alpha=0.3)
                
                cbar = fig_interaction.colorbar(scatter, ax=ax_current) # Use fig_interaction for colorbar
                cbar.set_label(target_column, fontsize=10)
                plot_idx +=1
            
            if plot_idx > 0 : # Only save if plots were made
                fig_interaction.suptitle('Feature Interaction Analysis with Target Variable', fontsize=16, fontweight='bold')
                plt.tight_layout(rect=[0, 0, 1, 0.96]) # Adjust layout to make space for suptitle
                interaction_file_path = os.path.join(output_folder, 'feature_interaction_analysis.png')
                plt.savefig(interaction_file_path, dpi=300, bbox_inches='tight')
                plt.close(fig_interaction)
                print(f"Feature interaction plot saved to {interaction_file_path}")
                eda_plot_paths['feature_interaction_analysis_plot'] = interaction_file_path
        else:
            print("Not enough top features for interaction plot or target not in numeric_df.")
        
        # --- Pairplot for selected features ---
        # Re-select top_correlated_features for pairplot
        correlation_matrix_for_pairplot = numeric_df.corr()
        top_correlated_features = correlation_matrix_for_pairplot[target_column].abs().sort_values(ascending=False).index[1:6] # Top 5
        pairplot_cols = [col for col in top_correlated_features if col in df.columns] + ([target_column] if target_column in df.columns else [])

        if len(pairplot_cols) > 1: # Pairplot needs at least 2 columns
            print(f"Generating pairplot for: {pairplot_cols}")
            g = sns.pairplot(df[pairplot_cols], diag_kind='kde', plot_kws={'alpha': 0.6})
            g.fig.suptitle('Pairwise Interactions of Top Correlated Features with Target', y=1.02, fontsize=14, fontweight='bold')
            pairplot_file_path = os.path.join(output_folder, 'EDA_pairplot.png')
            plt.savefig(pairplot_file_path, dpi=300, bbox_inches='tight')
            plt.close(g.fig)
            print(f"Pairplot saved to {pairplot_file_path}")
            eda_plot_paths['eda_pairplot'] = pairplot_file_path
        else:
            print("Not enough columns for pairplot after filtering.")
    else:
        print("Skipping feature interaction and pairplot due to no numeric features or target column issue.")
    
    return eda_plot_paths # Return all collected EDA plot paths