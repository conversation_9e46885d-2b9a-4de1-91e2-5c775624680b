import pandas as pd
import numpy as np
import os
from sklearn.preprocessing import OneHotEncoder, RobustScaler
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline

# 1. 数据加载
def load_data(file_path):
    """
    从CSV文件加载数据。
    """
    print("正在加载数据...")
    try:
        df = pd.read_csv(file_path)
        print("数据加载成功。")
        print("数据集形状:", df.shape)
        print("列名:", df.columns.tolist())
        return df
    except FileNotFoundError:
        print(f"错误: 文件 '{file_path}' 未找到。")
        return None

def feature_engineering(df):
    """
    根据材料科学原理进行特征工程，并记录其定义。
    """
    print("\n--- 开始特征工程 ---")
    
    feature_definitions = {
        'SiO2': "粉煤灰中SiO2的含量 (wt%)。",
        'Al2O3': "粉煤灰中Al2O3的含量 (wt%)。",
        'Phase': "粉煤灰中SiO2的晶型，分别为无定形和石英。",
        'Additives': "粘结剂类型。",
        'Additive_Ratio': "粘结剂与粉煤灰的质量比。",
        'water': "指在成型的过程中是否添加水。",
        'Na2CO3': "活化剂Na2CO3与粉煤灰中SiO2的摩尔比。",
        'Ca(OH)2': "Ca(OH)2与粉煤灰中SiO2的摩尔比。",
        'Temp': "煅烧温度 (摄氏度)。",
        'Time': "煅烧反应时间 (min)。",
        'granular': "成型方式，对应的powder指的是粉煤灰与Na2CO3和CaOH)2直接经过配料混合后的粉末,high_speed指的是使用高速造粒机进行造粒，balling指的是使用低速的滚球造粒机造粒，extruder指的是使用挤条机挤出造粒。",
        'Effective_Silicon': "有效硅含量 (wt%)，即目标变量。"
    }

    # --- 级别1: 活化剂比例 ---
    # 添加Na2CO3和Ca(OH)2的比值，避免除以零
    df['Na2CO3_CaOH2_Ratio'] = df['Na2CO3'] / (df['Ca(OH)2'] + 1e-6)
    feature_definitions['Na2CO3_CaOH2_Ratio'] = "活化剂Na2CO3与Ca(OH)2的摩尔比，计算为Na2CO3摩尔比 / (Ca(OH)2摩尔比 + 1e-6)。"

    # --- 级别2: 热力学与动力学特征 ---
    df['Temp_Time_Interaction'] = df['Temp'] * df['Time']
    feature_definitions['Temp_Time_Interaction'] = "温度与时间的交互项，计算为 Temp * Time。"

    return df, feature_definitions

# 3. 数据预处理和特征工程
def preprocess_data(df, target_column):
    """
    通过识别特征类型和定义预处理器来准备建模数据。
    数据分割和拟合将在交叉验证循环内处理，以防止数据泄露。
    """
    print("\n--- 正在定义预处理步骤 ---")
    
    # 1. 分离特征和目标
    X = df.drop(target_column, axis=1)
    y = df[target_column]

    # 2. 识别分类和数值特征
    categorical_cols = ['Phase', 'Additives', 'granular', 'water']
    for col in categorical_cols:
        if col in X.columns:
            X[col] = X[col].astype('category')
    
    categorical_features = X.select_dtypes(include=['object', 'category']).columns
    numerical_features = X.select_dtypes(include=np.number).columns
    
    print(f"已识别分类特征: {list(categorical_features)}")
    print(f"已识别数值特征: {list(numerical_features)}")
    
    # 3. 为数值和分类特征定义预处理管道
    # 对数值特征使用RobustScaler以处理异常值
    numerical_transformer = Pipeline(steps=[
        ('scaler', RobustScaler())
    ])

    # 对分类特征使用OneHotEncoder
    categorical_transformer = Pipeline(steps=[
        ('onehot', OneHotEncoder(handle_unknown='ignore', sparse_output=False))
    ])

    # 4. 创建ColumnTransformer预处理器
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', numerical_transformer, numerical_features),
            ('cat', categorical_transformer, categorical_features)
        ],
        remainder='passthrough'
    )
    
    print("--- 预处理器定义完成。---")
    # 返回未处理的数据和预处理器对象。
    # 拟合和转换将在交叉验证循环内部发生。
    return X, y, preprocessor