import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from sklearn.inspection import PartialDependenceDisplay
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.neighbors import K<PERSON>eighborsRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.svm import SVR
from sklearn.linear_model import BayesianRidge

# Check for SHAP availability
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
    print("Warning: SHAP library not found. SHAP-related functionalities will be disabled.")

# Check for additional ML libraries
try:
    import xgboost as xgb
    XGB_AVAILABLE = True
except ImportError:
    XGB_AVAILABLE = False

try:
    from lightgbm import LGBMRegressor
    LGBM_AVAILABLE = True
except ImportError:
    LGBM_AVAILABLE = False

try:
    from catboost import CatBoostRegressor
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

def safe_filename(name):
    """清理文件名，只保留字母、数字和空格，并去除末尾空格"""
    return "".join([c for c in name if c.isalpha() or c.isdigit() or c.isspace() or c in ['_', '-']]).rstrip()

def create_robust_shap_explainer(model, X_background, model_name="Unknown"):
    """
    为不同类型的模型创建合适的 SHAP 解释器
    """
    if not SHAP_AVAILABLE:
        return None
    
    try:
        # 树模型使用 TreeExplainer
        if isinstance(model, (RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor)):
            return shap.TreeExplainer(model)
        elif XGB_AVAILABLE and isinstance(model, xgb.XGBRegressor):
            return shap.TreeExplainer(model)
        elif LGBM_AVAILABLE and isinstance(model, LGBMRegressor):
            return shap.TreeExplainer(model)
        elif CATBOOST_AVAILABLE and isinstance(model, CatBoostRegressor):
            return shap.TreeExplainer(model)
        else:
            # 其他模型使用 KernelExplainer
            # 限制背景样本数量以提高性能
            background_sample = X_background.sample(min(50, len(X_background)), random_state=42) if len(X_background) > 50 else X_background
            return shap.KernelExplainer(model.predict, background_sample)
    except Exception as e:
        print(f"Warning: Could not create SHAP explainer for {model_name}: {e}")
        return None

def robust_shap_analysis(model, X_train, X_test, feature_names, output_folder, model_name="Model"):
    """
    执行稳健的 SHAP 分析，处理各种模型类型和潜在错误
    """
    if not SHAP_AVAILABLE:
        print("SHAP not available, skipping SHAP analysis")
        return []
    
    plot_paths = []
    plot_filename_prefix = safe_filename(model_name)
    
    try:
        print(f"Creating SHAP analysis for {model_name}...")
        
        # 确保数据是 DataFrame 格式
        if not isinstance(X_train, pd.DataFrame):
            X_train = pd.DataFrame(X_train, columns=feature_names)
        if not isinstance(X_test, pd.DataFrame):
            X_test = pd.DataFrame(X_test, columns=feature_names)
        
        # 创建 SHAP 解释器
        explainer = create_robust_shap_explainer(model, X_train, model_name)
        if explainer is None:
            print(f"Could not create SHAP explainer for {model_name}")
            return []
        
        # 限制测试样本数量以提高性能
        test_sample = X_test.sample(min(100, len(X_test)), random_state=42) if len(X_test) > 100 else X_test
        
        # 计算 SHAP 值
        if isinstance(explainer, shap.TreeExplainer):
            shap_values = explainer.shap_values(test_sample)
        else:
            shap_values = explainer.shap_values(test_sample)
        
        print(f"SHAP values computed for {model_name}, shape: {shap_values.shape}")
        
        # 生成 SHAP 条形图
        try:
            plt.figure(figsize=(10, 8))
            shap.summary_plot(shap_values, test_sample, plot_type="bar", show=False)
            plt.title(f'SHAP Feature Importance - {model_name}', fontsize=16, fontweight='bold')
            plt.tight_layout()
            bar_path = os.path.join(output_folder, f'shap_summary_bar_{plot_filename_prefix}.png')
            plt.savefig(bar_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            plot_paths.append(bar_path)
            print(f"SHAP bar plot saved: {bar_path}")
        except Exception as e:
            print(f"Error creating SHAP bar plot for {model_name}: {e}")
        
        # 生成 SHAP 散点图
        try:
            plt.figure(figsize=(10, 8))
            shap.summary_plot(shap_values, test_sample, show=False)
            plt.title(f'SHAP Summary Plot - {model_name}', fontsize=16, fontweight='bold')
            plt.tight_layout()
            dot_path = os.path.join(output_folder, f'shap_summary_dot_{plot_filename_prefix}.png')
            plt.savefig(dot_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            plot_paths.append(dot_path)
            print(f"SHAP dot plot saved: {dot_path}")
        except Exception as e:
            print(f"Error creating SHAP dot plot for {model_name}: {e}")
        
        # 生成前3个重要特征的依赖图
        try:
            feature_importance = np.abs(shap_values).mean(0)
            top_features_idx = np.argsort(feature_importance)[-3:]  # 前3个重要特征
            
            for i, feature_idx in enumerate(top_features_idx):
                feature_name = feature_names[feature_idx]
                plt.figure(figsize=(10, 6))
                shap.dependence_plot(feature_idx, shap_values, test_sample, show=False)
                plt.title(f'SHAP Dependence - {feature_name} ({model_name})', fontsize=14, fontweight='bold')
                plt.tight_layout()
                dep_path = os.path.join(output_folder, f'shap_dependence_{plot_filename_prefix}_{i}_{safe_filename(feature_name)}.png')
                plt.savefig(dep_path, dpi=300, bbox_inches='tight', facecolor='white')
                plt.close()
                plot_paths.append(dep_path)
                print(f"SHAP dependence plot saved: {dep_path}")
        except Exception as e:
            print(f"Error creating SHAP dependence plots for {model_name}: {e}")
            
    except Exception as e:
        print(f"Error in SHAP analysis for {model_name}: {e}")
    
    return plot_paths

def robust_pdp_analysis(model, X_data, feature_names, output_folder, model_name="Model"):
    """
    执行稳健的 PDP 分析，处理各种模型类型和潜在错误
    """
    plot_filename_prefix = safe_filename(model_name)
    print(f"Creating PDP analysis for {model_name}...")
    
    try:
        # 确保数据是 DataFrame 格式
        if not isinstance(X_data, pd.DataFrame):
            X_data = pd.DataFrame(X_data, columns=feature_names)
        
        # 选择前6个特征进行 PDP 分析
        n_display_features = min(6, len(feature_names))
        features_for_pdp = feature_names[:n_display_features]
        
        ncols = 3
        nrows = (n_display_features + ncols - 1) // ncols
        fig, axes = plt.subplots(nrows=nrows, ncols=ncols, figsize=(18, 6 * nrows), squeeze=False)
        axes = axes.flatten()
        
        success_count = 0
        for idx, feature in enumerate(features_for_pdp):
            ax = axes[idx]
            try:
                # 检查特征是否存在且有变化
                if feature not in X_data.columns:
                    ax.text(0.5, 0.5, f'Feature {feature}\nnot found', ha='center', va='center', color='red')
                    continue
                
                feature_values = X_data[feature]
                if feature_values.nunique() < 2:
                    ax.text(0.5, 0.5, f'Feature {feature}\nhas no variation', ha='center', va='center', color='orange')
                    continue
                
                # 创建 PDP
                PartialDependenceDisplay.from_estimator(
                    model, X_data, [feature], ax=ax, n_jobs=1, grid_resolution=50
                )
                ax.set_title(f'{feature}', fontsize=14, fontweight='bold')
                ax.set_xlabel(feature, fontsize=12, fontweight='bold')
                ax.set_ylabel('Partial Dependence', fontsize=12, fontweight='bold')
                success_count += 1
                
            except Exception as e:
                ax.text(0.5, 0.5, f'PDP Failed\n{feature}\n{str(e)[:20]}...', 
                       ha='center', va='center', color='red', fontsize=10)
                print(f"    ERROR generating PDP for {feature}: {e}")
        
        # 隐藏未使用的子图
        for idx in range(n_display_features, len(axes)):
            axes[idx].set_visible(False)
        
        fig.suptitle(f'Partial Dependence Analysis - {model_name} ({success_count}/{n_display_features} successful)', 
                    fontsize=18, fontweight='bold', y=0.98)
        plt.tight_layout(rect=[0, 0, 1, 0.95])
        
        pdp_path = os.path.join(output_folder, f'pdp_analysis_{plot_filename_prefix}.png')
        plt.savefig(pdp_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close(fig)
        
        if success_count > 0:
            print(f"PDP analysis saved: {pdp_path} ({success_count}/{n_display_features} features successful)")
        else:
            print(f"PDP analysis failed for all features in {model_name}")
        
        return pdp_path if success_count > 0 else None
        
    except Exception as e:
        print(f"Error in PDP analysis for {model_name}: {e}")
        return None
