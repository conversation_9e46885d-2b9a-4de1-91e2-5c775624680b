"""
Perplexity AI configuration and optimization for Agent Laboratory.
This module provides configuration settings and optimization strategies for using Perplexity AI
as the primary literature review engine in Agent Laboratory.
"""

import os
from typing import Dict, List, Optional

class PerplexityConfig:
    """Configuration class for Perplexity AI integration."""
    
    # Model preferences (ordered by capability and cost-effectiveness)
    MODEL_PREFERENCES = {
        "comprehensive_review": "sonar-pro",  # Best for detailed literature reviews
        "quick_search": "sonar-small",        # Cost-effective for quick searches
        "deep_analysis": "sonar-huge",        # Most capable for complex analysis
    }
    
    # Search domain filters for different research areas
    DOMAIN_FILTERS = {
        "machine_learning": [
            "arxiv.org", 
            "scholar.google.com", 
            "pubmed.ncbi.nlm.nih.gov",
            "ieeexplore.ieee.org"
        ],
        "materials_science": [
            "pubmed.ncbi.nlm.nih.gov",
            "arxiv.org",
            "scholar.google.com",
            "nature.com"
        ],
        "biomedical": [
            "pubmed.ncbi.nlm.nih.gov",
            "scholar.google.com",
            "arxiv.org"
        ],
        "general": [
            "pubmed.ncbi.nlm.nih.gov",
            "arxiv.org", 
            "scholar.google.com"
        ]
    }
    
    # Literature review templates
    REVIEW_TEMPLATES = {
        "comprehensive": """
        Please conduct a comprehensive literature review on: "{topic}"
        
        Structure your review as follows:
        1. **Introduction and Background**
        2. **Current State of Research**
        3. **Key Methodologies and Approaches**
        4. **Recent Developments (2023-2024)**
        5. **Research Gaps and Limitations**
        6. **Future Directions**
        7. **Conclusion**
        
        For each section, provide:
        - Key findings from recent papers
        - Methodological approaches used
        - Quantitative results where available
        - Proper citations
        
        Focus on papers from the last 3-5 years, with emphasis on highly cited works.
        """,
        
        "focused": """
        Please provide a focused literature review on: "{topic}"
        
        Focus on:
        1. **Core Concepts and Definitions**
        2. **Key Research Findings**
        3. **Main Methodological Approaches**
        4. **Current Challenges**
        5. **Recent Breakthroughs**
        
        Prioritize recent, high-impact publications with clear experimental results.
        """,
        
        "methods_focused": """
        Please analyze the methodological landscape for: "{topic}"
        
        Cover:
        1. **Established Methods and Techniques**
        2. **Emerging Approaches**
        3. **Comparative Studies**
        4. **Performance Metrics and Benchmarks**
        5. **Implementation Challenges**
        6. **Best Practices**
        
        Include quantitative comparisons where available.
        """
    }
    
    @staticmethod
    def get_optimal_model(task_type: str) -> str:
        """Get the optimal model for a specific task type."""
        return PerplexityConfig.MODEL_PREFERENCES.get(task_type, "sonar-pro")
    
    @staticmethod
    def get_domain_filter(research_area: str) -> List[str]:
        """Get appropriate domain filters for a research area."""
        # Try to match research area keywords
        area_lower = research_area.lower()
        
        if any(keyword in area_lower for keyword in ["machine learning", "ai", "neural", "deep learning"]):
            return PerplexityConfig.DOMAIN_FILTERS["machine_learning"]
        elif any(keyword in area_lower for keyword in ["material", "chemistry", "physics"]):
            return PerplexityConfig.DOMAIN_FILTERS["materials_science"]
        elif any(keyword in area_lower for keyword in ["medical", "biology", "biomedical", "health"]):
            return PerplexityConfig.DOMAIN_FILTERS["biomedical"]
        else:
            return PerplexityConfig.DOMAIN_FILTERS["general"]
    
    @staticmethod
    def get_review_template(template_type: str, topic: str) -> str:
        """Get a formatted review template for a specific topic."""
        template = PerplexityConfig.REVIEW_TEMPLATES.get(template_type, 
                                                        PerplexityConfig.REVIEW_TEMPLATES["comprehensive"])
        return template.format(topic=topic)
    
    @staticmethod
    def estimate_cost(num_queries: int, avg_tokens_per_query: int = 2000, model: str = "sonar-pro") -> float:
        """Estimate the cost of Perplexity API usage."""
        cost_per_token = {
            "sonar-pro": 1.00 / 1000000,
            "sonar-small": 0.20 / 1000000,
            "sonar-huge": 5.00 / 1000000
        }
        
        rate = cost_per_token.get(model, cost_per_token["sonar-pro"])
        # Estimate both input and output tokens (roughly equal)
        total_tokens = num_queries * avg_tokens_per_query * 2
        return total_tokens * rate


class PerplexityOptimizer:
    """Optimizer for Perplexity AI usage in Agent Laboratory."""
    
    def __init__(self, api_key: str, budget_limit: float = 10.0):
        """
        Initialize the optimizer.
        
        Args:
            api_key: Perplexity API key
            budget_limit: Maximum budget in USD for the session
        """
        self.api_key = api_key
        self.budget_limit = budget_limit
        self.current_cost = 0.0
        self.query_count = 0
    
    def can_afford_query(self, estimated_tokens: int = 2000, model: str = "sonar-pro") -> bool:
        """Check if we can afford another query within budget."""
        estimated_cost = PerplexityConfig.estimate_cost(1, estimated_tokens, model)
        return (self.current_cost + estimated_cost) <= self.budget_limit
    
    def recommend_strategy(self, research_topic: str, num_papers_needed: int) -> Dict[str, str]:
        """Recommend an optimal strategy for literature review."""
        strategy = {
            "primary_approach": "perplexity_comprehensive",
            "model": "sonar-pro",
            "template": "comprehensive",
            "estimated_cost": 0.0,
            "steps": []
        }
        
        # Estimate cost for comprehensive review
        estimated_cost = PerplexityConfig.estimate_cost(3, 3000, "sonar-pro")  # 3 queries, 3k tokens each
        
        if estimated_cost > self.budget_limit:
            # Use cost-effective approach
            strategy.update({
                "model": "sonar-small",
                "template": "focused",
                "estimated_cost": PerplexityConfig.estimate_cost(2, 2000, "sonar-small")
            })
            strategy["steps"] = [
                "Use focused literature review template",
                "Single comprehensive query with sonar-small model",
                "Supplement with ArXiv if needed"
            ]
        else:
            strategy["estimated_cost"] = estimated_cost
            strategy["steps"] = [
                "Comprehensive literature review with sonar-pro",
                "Recent developments search",
                "Research gaps analysis",
                "Methodological approaches review"
            ]
        
        return strategy
    
    def update_cost(self, actual_cost: float):
        """Update the current cost tracking."""
        self.current_cost += actual_cost
        self.query_count += 1
    
    def get_usage_summary(self) -> Dict[str, float]:
        """Get usage summary."""
        return {
            "total_cost": self.current_cost,
            "budget_remaining": self.budget_limit - self.current_cost,
            "budget_used_percent": (self.current_cost / self.budget_limit) * 100,
            "queries_made": self.query_count
        }


def setup_perplexity_for_agent_lab(research_topic: str, budget: float = 10.0) -> Dict:
    """
    Setup optimal Perplexity configuration for Agent Laboratory.
    
    Args:
        research_topic: The research topic for literature review
        budget: Budget limit in USD
        
    Returns:
        Configuration dictionary
    """
    api_key = os.getenv('PERPLEXITY_API_KEY')
    if not api_key:
        return {"error": "PERPLEXITY_API_KEY not found in environment variables"}
    
    optimizer = PerplexityOptimizer(api_key, budget)
    strategy = optimizer.recommend_strategy(research_topic, 5)  # Assume 5 papers needed
    
    config = {
        "api_key": api_key,
        "strategy": strategy,
        "domain_filters": PerplexityConfig.get_domain_filter(research_topic),
        "review_template": PerplexityConfig.get_review_template(strategy["template"], research_topic),
        "optimizer": optimizer
    }
    
    return config
